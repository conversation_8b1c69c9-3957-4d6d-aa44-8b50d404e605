<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WiseVideo Logo 设计指南</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="max-w-6xl mx-auto py-12 px-6">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">WiseVideo 品牌 Logo 设计</h1>
            <p class="text-lg text-gray-600">AI/WV 双重视觉效果的智能品牌标识系统</p>
            <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                <p class="text-sm text-blue-800">
                    <strong>独特设计概念：</strong>这个Logo巧妙地结合了几何图形，从不同角度可以看到 "AI" 和 "WV" 两组字母，象征着人工智能与 WiseVideo 的完美融合。
                </p>
            </div>
        </div>

        <!-- Dual Visual Effect Explanation -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 mb-8 border border-blue-200">
            <h2 class="text-2xl font-semibold mb-6 text-gray-900">双重视觉效果解析</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-blue-800">🎯 视觉角度 1: "AI"</h3>
                    <ul class="text-sm text-gray-700 space-y-2">
                        <li><strong>清晰 "A" 字：</strong>左侧大三角形 + 明显横梁，标准A字结构一目了然</li>
                        <li><strong>明确 "I" 字：</strong>右侧三角形主体 + 顶部圆点，经典I字特征突出</li>
                        <li><strong>整体印象：</strong>强调人工智能(AI)技术属性，识别度极高</li>
                    </ul>
                </div>
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-purple-800">🎯 视觉角度 2: "WV"</h3>
                    <ul class="text-sm text-gray-700 space-y-2">
                        <li><strong>宽阔 "W" 字：</strong>左侧大三角形 + 横梁展现W字宽度特征</li>
                        <li><strong>尖锐 "V" 字：</strong>右侧三角形完美呈现经典V字轮廓</li>
                        <li><strong>整体印象：</strong>清晰体现 WiseVideo 品牌缩写标识</li>
                    </ul>
                </div>
            </div>
            <div class="mt-6 p-4 bg-white rounded-lg border border-gray-200">
                <p class="text-sm text-gray-600 italic">
                    💡 <strong>设计升级：</strong>新版本采用更清晰的几何结构，让字母形状一目了然。通过明显的三角形轮廓、横向矩形横梁和标识性圆点，确保"AI"和"WV"字母在任何观察角度下都能被清晰识别，完美平衡了创意性与实用性。
                </p>
            </div>
        </div>

        <!-- Primary Logo -->
        <div class="bg-white rounded-xl p-8 mb-8 shadow-sm border">
            <h2 class="text-2xl font-semibold mb-6 text-gray-900">主要Logo</h2>
            <div class="flex justify-center items-center py-8 bg-gray-50 rounded-lg">
                <svg width="200" height="60" viewBox="0 0 200 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <!-- Main logo icon with clear AI/WV letter design -->
                    <g transform="translate(5, 5)">
                        <!-- Outer container circle -->
                        <circle cx="25" cy="25" r="24" fill="url(#gradient1)" stroke="url(#gradient2)" stroke-width="1.5"/>
                        
                        <!-- Clear letter design -->
                        <g transform="translate(25, 25)">
                            <!-- Letter "A" (also forms "W" when viewed differently) -->
                            <g fill="white" opacity="0.95">
                                <!-- A letter main structure -->
                                <path d="M-15 12 L-9 -12 L-3 12 Z" stroke="white" stroke-width="1"/>
                                <!-- A crossbar (also W middle) -->
                                <rect x="-12" y="0" width="6" height="2.5" rx="1"/>
                            </g>
                            
                            <!-- Letter "I" (also forms "V" when viewed differently) -->
                            <g fill="white" opacity="0.95">
                                <!-- I letter main body (also V shape) -->
                                <path d="M3 12 L9 -12 L15 12 Z" stroke="white" stroke-width="1"/>
                                <!-- I dot (distinctive feature) -->
                                <circle cx="9" cy="-18" r="2.5"/>
                            </g>
                            
                            <!-- Connection element (enhances readability) -->
                            <rect x="-1" y="10" width="2" height="4" fill="white" opacity="0.8" rx="1"/>
                            
                            <!-- Tech accent elements -->
                            <g opacity="0.5">
                                <line x1="-18" y1="2" x2="-16" y2="2" stroke="white" stroke-width="1.5"/>
                                <line x1="16" y1="2" x2="18" y2="2" stroke="white" stroke-width="1.5"/>
                                <circle cx="0" cy="16" r="1" fill="white"/>
                            </g>
                        </g>
                    </g>
                    
                    <!-- Brand text -->
                    <text x="65" y="25" font-family="Inter, system-ui, -apple-system, sans-serif" font-size="18" font-weight="700" fill="#1a1a1a">
                        Wise
                    </text>
                    <text x="65" y="42" font-family="Inter, system-ui, -apple-system, sans-serif" font-size="18" font-weight="500" fill="#6366f1">
                        Video
                    </text>
                    
                    <!-- Subtle accent elements -->
                    <g opacity="0.6">
                        <circle cx="125" cy="20" r="1.5" fill="#6366f1"/>
                        <circle cx="135" cy="25" r="1" fill="#8b5cf6"/>
                        <circle cx="145" cy="18" r="0.8" fill="#06b6d4"/>
                        <path d="M128 22 L132 22" stroke="#6366f1" stroke-width="0.8"/>
                    </g>
                    
                    <!-- Gradients -->
                    <defs>
                        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
                            <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
                        </linearGradient>
                        <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                </svg>
            </div>
        </div>

        <!-- Logo Variations -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Icon Only -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-xl font-semibold mb-4 text-gray-900">图标版本</h3>
                <div class="flex justify-center items-center py-6 bg-gray-50 rounded-lg">
                    <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g transform="translate(5, 5)">
                            <circle cx="25" cy="25" r="24" fill="url(#gradient1-icon)" stroke="url(#gradient2-icon)" stroke-width="1.5"/>
                            
                            <g transform="translate(25, 25)">
                                <!-- Letter "A" (also forms "W" when viewed differently) -->
                                <g fill="white" opacity="0.95">
                                    <!-- A letter main structure -->
                                    <path d="M-15 12 L-9 -12 L-3 12 Z" stroke="white" stroke-width="1"/>
                                    <!-- A crossbar (also W middle) -->
                                    <rect x="-12" y="0" width="6" height="2.5" rx="1"/>
                                </g>
                                
                                <!-- Letter "I" (also forms "V" when viewed differently) -->
                                <g fill="white" opacity="0.95">
                                    <!-- I letter main body (also V shape) -->
                                    <path d="M3 12 L9 -12 L15 12 Z" stroke="white" stroke-width="1"/>
                                    <!-- I dot (distinctive feature) -->
                                    <circle cx="9" cy="-18" r="2.5"/>
                                </g>
                                
                                <!-- Connection element -->
                                <rect x="-1" y="10" width="2" height="4" fill="white" opacity="0.8" rx="1"/>
                                
                                <!-- Tech accent elements -->
                                <g opacity="0.5">
                                    <line x1="-18" y1="2" x2="-16" y2="2" stroke="white" stroke-width="1.5"/>
                                    <line x1="16" y1="2" x2="18" y2="2" stroke="white" stroke-width="1.5"/>
                                    <circle cx="0" cy="16" r="1" fill="white"/>
                                </g>
                            </g>
                        </g>
                        <defs>
                            <linearGradient id="gradient1-icon" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="gradient2-icon" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                    </svg>
                </div>
                <p class="text-sm text-gray-600 mt-4">用于app图标、社交媒体头像等正方形场景</p>
            </div>

            <!-- Horizontal Layout -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-xl font-semibold mb-4 text-gray-900">横向布局</h3>
                <div class="flex justify-center items-center py-6 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <svg width="40" height="40" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g transform="translate(5, 5)">
                                <circle cx="25" cy="25" r="24" fill="url(#gradient1-h)" stroke="url(#gradient2-h)" stroke-width="1.5"/>
                                
                                <g transform="translate(25, 25)">
                                    <!-- Letter "A" (also forms "W" when viewed differently) -->
                                    <g fill="white" opacity="0.95">
                                        <!-- A letter main structure -->
                                        <path d="M-15 12 L-9 -12 L-3 12 Z" stroke="white" stroke-width="1"/>
                                        <!-- A crossbar (also W middle) -->
                                        <rect x="-12" y="0" width="6" height="2.5" rx="1"/>
                                    </g>
                                    
                                    <!-- Letter "I" (also forms "V" when viewed differently) -->
                                    <g fill="white" opacity="0.95">
                                        <!-- I letter main body (also V shape) -->
                                        <path d="M3 12 L9 -12 L15 12 Z" stroke="white" stroke-width="1"/>
                                        <!-- I dot (distinctive feature) -->
                                        <circle cx="9" cy="-18" r="2.5"/>
                                    </g>
                                    
                                    <!-- Connection element -->
                                    <rect x="-1" y="10" width="2" height="4" fill="white" opacity="0.8" rx="1"/>
                                    
                                    <!-- Tech accent elements -->
                                    <g opacity="0.5">
                                        <line x1="-18" y1="2" x2="-16" y2="2" stroke="white" stroke-width="1.5"/>
                                        <line x1="16" y1="2" x2="18" y2="2" stroke="white" stroke-width="1.5"/>
                                        <circle cx="0" cy="16" r="1" fill="white"/>
                                    </g>
                                </g>
                            </g>
                            <defs>
                                <linearGradient id="gradient1-h" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
                                    <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="gradient2-h" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                        </svg>
                        <div class="text-left">
                            <div class="text-lg font-bold text-gray-900">WiseVideo</div>
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-600 mt-4">用于导航栏、名片等水平空间布局</p>
            </div>
        </div>

        <!-- Dark Background Versions -->
        <div class="bg-gray-900 rounded-xl p-8 mb-8">
            <h2 class="text-2xl font-semibold mb-6 text-white">深色背景版本</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="flex justify-center items-center py-8 bg-gray-800 rounded-lg">
                    <svg width="200" height="60" viewBox="0 0 200 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g transform="translate(5, 5)">
                            <circle cx="25" cy="25" r="24" fill="url(#gradient1-dark)" stroke="url(#gradient2-dark)" stroke-width="1.5"/>
                            
                            <g transform="translate(25, 25)">
                                <!-- Letter "A" (also forms "W" when viewed differently) -->
                                <g fill="white" opacity="0.95">
                                    <!-- A letter main structure -->
                                    <path d="M-15 12 L-9 -12 L-3 12 Z" stroke="white" stroke-width="1"/>
                                    <!-- A crossbar (also W middle) -->
                                    <rect x="-12" y="0" width="6" height="2.5" rx="1"/>
                                </g>
                                
                                <!-- Letter "I" (also forms "V" when viewed differently) -->
                                <g fill="white" opacity="0.95">
                                    <!-- I letter main body (also V shape) -->
                                    <path d="M3 12 L9 -12 L15 12 Z" stroke="white" stroke-width="1"/>
                                    <!-- I dot (distinctive feature) -->
                                    <circle cx="9" cy="-18" r="2.5"/>
                                </g>
                                
                                <!-- Connection element -->
                                <rect x="-1" y="10" width="2" height="4" fill="white" opacity="0.8" rx="1"/>
                                
                                <!-- Tech accent elements -->
                                <g opacity="0.6">
                                    <line x1="-18" y1="2" x2="-16" y2="2" stroke="white" stroke-width="1.5"/>
                                    <line x1="16" y1="2" x2="18" y2="2" stroke="white" stroke-width="1.5"/>
                                    <circle cx="0" cy="16" r="1" fill="white"/>
                                </g>
                            </g>
                        </g>
                        <text x="65" y="25" font-family="Inter, system-ui, -apple-system, sans-serif" font-size="18" font-weight="700" fill="white">
                            Wise
                        </text>
                        <text x="65" y="42" font-family="Inter, system-ui, -apple-system, sans-serif" font-size="18" font-weight="500" fill="#60a5fa">
                            Video
                        </text>
                        <g opacity="0.6">
                            <circle cx="125" cy="20" r="1.5" fill="#60a5fa"/>
                            <circle cx="135" cy="25" r="1" fill="#a78bfa"/>
                            <circle cx="145" cy="18" r="0.8" fill="#22d3ee"/>
                            <path d="M128 22 L132 22" stroke="#60a5fa" stroke-width="0.8"/>
                        </g>
                        <defs>
                            <linearGradient id="gradient1-dark" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="gradient2-dark" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#3730a3;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#0e7490;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                    </svg>
                </div>
                <div class="flex justify-center items-center py-8 bg-gray-700 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <svg width="40" height="40" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g transform="translate(5, 5)">
                                <circle cx="25" cy="25" r="24" fill="url(#gradient1-dark2)" stroke="url(#gradient2-dark2)" stroke-width="1.5"/>
                                
                                <g transform="translate(25, 25)">
                                    <!-- Letter "A" (also forms "W" when viewed differently) -->
                                    <g fill="white" opacity="0.95">
                                        <!-- A letter main structure -->
                                        <path d="M-15 12 L-9 -12 L-3 12 Z" stroke="white" stroke-width="1"/>
                                        <!-- A crossbar (also W middle) -->
                                        <rect x="-12" y="0" width="6" height="2.5" rx="1"/>
                                    </g>
                                    
                                    <!-- Letter "I" (also forms "V" when viewed differently) -->
                                    <g fill="white" opacity="0.95">
                                        <!-- I letter main body (also V shape) -->
                                        <path d="M3 12 L9 -12 L15 12 Z" stroke="white" stroke-width="1"/>
                                        <!-- I dot (distinctive feature) -->
                                        <circle cx="9" cy="-18" r="2.5"/>
                                    </g>
                                    
                                    <!-- Connection element -->
                                    <rect x="-1" y="10" width="2" height="4" fill="white" opacity="0.8" rx="1"/>
                                    
                                    <!-- Tech accent elements -->
                                    <g opacity="0.6">
                                        <line x1="-18" y1="2" x2="-16" y2="2" stroke="white" stroke-width="1.5"/>
                                        <line x1="16" y1="2" x2="18" y2="2" stroke="white" stroke-width="1.5"/>
                                        <circle cx="0" cy="16" r="1" fill="white"/>
                                    </g>
                                </g>
                            </g>
                            <defs>
                                <linearGradient id="gradient1-dark2" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
                                    <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="gradient2-dark2" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#3730a3;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#0e7490;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                        </svg>
                        <div class="text-left">
                            <div class="text-lg font-bold text-white">WiseVideo</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Design Specifications -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Color Palette -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-xl font-semibold mb-4 text-gray-900">色彩规范</h3>
                <div class="space-y-3">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 rounded bg-gradient-to-r from-indigo-500 to-purple-500"></div>
                        <div>
                            <p class="font-medium text-gray-900">主色渐变</p>
                            <p class="text-sm text-gray-600">#6366f1 → #8b5cf6</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 rounded bg-cyan-500"></div>
                        <div>
                            <p class="font-medium text-gray-900">强调色</p>
                            <p class="text-sm text-gray-600">#06b6d4</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 rounded bg-gray-900"></div>
                        <div>
                            <p class="font-medium text-gray-900">文字主色</p>
                            <p class="text-sm text-gray-600">#1a1a1a</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Typography -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-xl font-semibold mb-4 text-gray-900">字体规范</h3>
                <div class="space-y-3">
                    <div>
                        <p class="font-bold text-lg text-gray-900">Wise (粗体)</p>
                        <p class="text-sm text-gray-600">Inter Bold 700</p>
                    </div>
                    <div>
                        <p class="font-medium text-lg text-indigo-600">Video (中等)</p>
                        <p class="text-sm text-gray-600">Inter Medium 500</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">建议使用 Inter 字体族以保持一致性</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Guidelines -->
        <div class="bg-white rounded-xl p-8 shadow-sm border">
            <h2 class="text-2xl font-semibold mb-6 text-gray-900">使用指南</h2>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">✅ 推荐使用</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 保持足够的留白空间</li>
                        <li>• 在高对比度背景上使用</li>
                        <li>• 保持原始比例缩放</li>
                        <li>• 使用提供的颜色变体</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">❌ 避免做法</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 不要拉伸或变形Logo</li>
                        <li>• 不要改变颜色或渐变</li>
                        <li>• 不要添加阴影或特效</li>
                        <li>• 不要在复杂背景上使用</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">📏 尺寸规范</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 最小宽度: 100px</li>
                        <li>• 网站头部: 150-200px</li>
                        <li>• 印刷材料: 300DPI</li>
                        <li>• 应用图标: 512×512px</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Implementation Code -->
        <div class="bg-gray-900 rounded-xl p-8 mt-8">
            <h2 class="text-2xl font-semibold mb-6 text-white">React 组件代码</h2>
            <div class="bg-gray-800 rounded-lg p-4 overflow-x-auto">
                <pre class="text-green-400 text-sm"><code>// WiseVideoLogo.tsx
import React from 'react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'full' | 'icon' | 'horizontal';
  theme?: 'light' | 'dark';
}

export const WiseVideoLogo: React.FC&lt;LogoProps&gt; = ({ 
  size = 'md', 
  variant = 'full',
  theme = 'light' 
}) => {
  const sizes = {
    sm: { width: 120, height: 36 },
    md: { width: 200, height: 60 },
    lg: { width: 300, height: 90 }
  };

  return (
    &lt;svg 
      width={sizes[size].width} 
      height={sizes[size].height}
      viewBox="0 0 200 60" 
      className="wise-video-logo"
    &gt;
      {/* SVG 内容 */}
    &lt;/svg&gt;
  );
};</code></pre>
            </div>
        </div>
    </div>
</body>
</html> 