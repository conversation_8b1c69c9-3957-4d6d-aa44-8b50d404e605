# AI Generator Platform - 功能结构说明

## 项目概述
基于 prototype.html 的 AI 生成平台，提供多种 AI 工具（图像、视频、音频、3D 模型生成）的 Web 应用。

## 页面结构分析

### 1. 首页 (Home Page)
- **功能**: 平台介绍、特性展示、统计数据
- **组件**: 
  - Hero Section（主标题、CTA按钮）
  - Features Section（特性卡片）
  - Stats Section（统计数据）
- **路由**: `/`

### 2. 认证页面
#### 2.1 登录页面 (Login)
- **功能**: 用户登录、第三方登录（Google、GitHub）
- **路由**: `/login`

#### 2.2 注册页面 (Register)
- **功能**: 用户注册、表单验证
- **路由**: `/register`

#### 2.3 忘记密码页面 (Forgot Password)
- **功能**: 发送重置密码邮件
- **路由**: `/forgot-password`

#### 2.4 重置密码页面 (Reset Password)
- **功能**: 重置密码、密码强度验证
- **路由**: `/reset-password`

### 3. 仪表板 (Dashboard)
- **功能**: 
  - 统计卡片（总任务、已完成、处理中、API使用率）
  - 快速操作（生成图像、视频、音频、3D模型）
  - 最近任务列表
- **路由**: `/dashboard`

### 4. AI 工具页面 (AI Tools)
- **功能**:
  - 工具分类筛选（全部、图像、视频、音频、3D）
  - 工具卡片展示
  - 工具模态框（参数配置、生成）
- **支持的工具**:
  - 图像生成: Flux-Dev, SDXL
  - 视频生成: Veo2-T2V
  - 音频生成: DIA-TTS
  - 3D生成: Hunyuan3D-v2
- **路由**: `/tools`

### 5. 任务历史页面 (Tasks)
- **功能**:
  - 任务列表（状态筛选、类型筛选）
  - 任务详情（预览、下载、分享、删除）
  - 进度显示、错误处理
  - 分页功能
- **路由**: `/tasks`

### 6. 定价页面 (Pricing)
- **功能**:
  - 定价方案展示
  - 月付/年付切换
  - 功能对比表
- **路由**: `/pricing`

### 7. 用户相关页面
#### 7.1 个人资料 (Profile)
- **功能**: 用户信息编辑、头像上传
- **路由**: `/profile`

#### 7.2 设置页面 (Settings)
- **功能**: 账户设置、通知设置、API密钥管理
- **路由**: `/settings`

#### 7.3 账单页面 (Billing)
- **功能**: 账单历史、支付方式管理、使用量统计
- **路由**: `/billing`

### 8. 管理员页面 (Admin)
- **功能**: 用户管理、系统监控、工具管理
- **路由**: `/admin`
- **权限**: 仅管理员可访问

## 组件结构

### 1. 布局组件
- `AppLayout.vue` - 主布局（导航栏 + 内容区域）
- `AppNavigation.vue` - 顶部导航栏
- `UserMenu.vue` - 用户下拉菜单

### 2. 功能组件
- `ToolCard.vue` - AI工具卡片
- `TaskItem.vue` - 任务列表项
- `StatsCard.vue` - 统计卡片
- `ToolModal.vue` - 工具使用模态框
- `PricingCard.vue` - 定价方案卡片

### 3. 通用组件
- `LoadingSpinner.vue` - 加载动画
- `ProgressBar.vue` - 进度条
- `FileUpload.vue` - 文件上传
- `ImagePreview.vue` - 图片预览

## 状态管理

### 1. User Store
- 用户信息、认证状态
- 登录、注册、登出功能

### 2. Task Store
- 任务列表管理
- 任务状态更新
- 任务筛选和分页

### 3. Tool Store
- 工具列表和配置
- 工具使用历史
- 工具参数管理

### 4. UI Store
- 全局UI状态
- 模态框状态
- 通知消息

## API 接口规划

### 1. 认证接口
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/logout` - 用户登出
- `POST /api/v1/auth/forgot-password` - 忘记密码
- `POST /api/v1/auth/reset-password` - 重置密码
- `GET /api/v1/auth/me` - 获取当前用户信息

### 2. 任务接口
- `GET /api/v1/tasks` - 获取任务列表
- `POST /api/v1/tasks` - 创建新任务
- `GET /api/v1/tasks/:id` - 获取任务详情
- `DELETE /api/v1/tasks/:id` - 删除任务
- `POST /api/v1/tasks/:id/cancel` - 取消任务

### 3. 工具接口
- `GET /api/v1/tools` - 获取工具列表
- `POST /api/v1/tools/:toolId/generate` - 使用工具生成内容
- `GET /api/v1/tools/:toolId/config` - 获取工具配置

### 4. 用户接口
- `GET /api/v1/user/profile` - 获取用户资料
- `PUT /api/v1/user/profile` - 更新用户资料
- `GET /api/v1/user/stats` - 获取用户统计
- `GET /api/v1/user/billing` - 获取账单信息

### 5. 管理员接口
- `GET /api/v1/admin/users` - 获取用户列表
- `GET /api/v1/admin/stats` - 获取系统统计
- `GET /api/v1/admin/tools` - 管理工具配置
