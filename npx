> create-vue . --typescript --router --pinia --eslint --prettier

┌  Vue.js - The Progressive JavaScript Framework
│
◇  Current directory is not empty. Remove existing files and continue
│  Yes
│
◇  Package name:
│  wise-video-fe

Scaffolding project in /Users/<USER>/projects/wise_video_fe...
│
└  Done. Now run:

   npm install
   npm run format
   npm run dev

| Optional: Initialize Git in your project directory with:
   
   git init && git add -A && git commit -m "initial commit"



npm install



