<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Generator - Platform Prototype</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .page { display: none; }
        .page.active { display: block; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .glass { backdrop-filter: blur(10px); background: rgba(255, 255, 255, 0.1); }
        .hover-scale { transition: transform 0.2s; }
        .hover-scale:hover { transform: scale(1.05); }
        .user-menu { 
            display: none; 
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.2s ease-in-out;
        }
        .user-menu.show { 
            display: block; 
            opacity: 1;
            transform: translateY(0);
        }
        .logged-out { display: block; }
        .logged-in { display: none; }
        .user-logged-in .logged-out { display: none; }
        .user-logged-in .logged-in { display: block; }
    </style>
</head>
<body class="bg-gray-50" id="mainBody">

<!-- Navigation -->
<nav class="bg-white shadow-lg fixed w-full top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex items-center">
                <div class="flex-shrink-0 flex items-center">
                    <i class="fas fa-robot text-2xl text-blue-600 mr-2"></i>
                    <span class="text-xl font-bold text-gray-800">AI Generator</span>
                </div>
            </div>
            <div class="hidden md:flex items-center space-x-8">
                <a href="#" onclick="showPage('home')" class="text-gray-700 hover:text-blue-600 nav-link">Home</a>
                <a href="#" onclick="showPage('dashboard')" class="text-gray-700 hover:text-blue-600 nav-link logged-in">Dashboard</a>
                <a href="#" onclick="showPage('tools')" class="text-gray-700 hover:text-blue-600 nav-link">AI Tools</a>
                <a href="#" onclick="showPage('tasks')" class="text-gray-700 hover:text-blue-600 nav-link logged-in">Tasks</a>
                <a href="#" onclick="showPage('pricing')" class="text-gray-700 hover:text-blue-600 nav-link">Pricing</a>
                <a href="#" onclick="showPage('admin')" class="text-gray-700 hover:text-blue-600 nav-link logged-in">Admin</a>
                
                <!-- Logged out state -->
                <button onclick="showPage('login')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 logged-out">Sign In</button>
                
                <!-- Logged in state -->
                <div class="logged-in relative">
                    <!-- User Menu -->
                    <div class="relative">
                        <button onclick="toggleUserMenu()" class="flex items-center space-x-2 text-gray-700 hover:text-blue-600">
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                JD
                            </div>
                            <span class="font-medium">John Doe</span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        
                        <!-- Dropdown Menu -->
                        <div id="userMenu" class="user-menu absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                            <div class="py-2">
                                <div class="px-4 py-2 border-b border-gray-200">
                                    <p class="text-sm font-medium text-gray-900">John Doe</p>
                                    <p class="text-sm text-gray-500"><EMAIL></p>
                                </div>
                                <a href="#" onclick="showPage('profile')" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user mr-2"></i>Profile
                                </a>
                                <a href="#" onclick="showPage('settings')" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-cog mr-2"></i>Settings
                                </a>
                                <a href="#" onclick="showPage('billing')" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-credit-card mr-2"></i>Billing
                                </a>
                                <div class="border-t border-gray-200"></div>
                                <a href="#" onclick="logout()" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Sign Out
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- Home Page -->
<div id="home" class="page active">
    <div class="pt-16">
        <!-- Hero Section -->
        <section class="gradient-bg text-white py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-5xl font-bold mb-6">Build Your Next<br><span class="text-yellow-300">AI-Powered Application</span></h1>
                <p class="text-xl mb-8 max-w-3xl mx-auto opacity-90">Transform your ideas into reality with our cutting-edge AI platform. Designed for developers, by developers.</p>
                <div class="flex justify-center space-x-4">
                    <button onclick="showPage('dashboard')" class="bg-yellow-500 text-black px-8 py-3 rounded-lg font-semibold hover:bg-yellow-400 hover-scale">
                        <i class="fas fa-rocket mr-2"></i>Get Started
                    </button>
                    <button onclick="showPage('pricing')" class="bg-transparent border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-gray-800 hover-scale">
                        <i class="fas fa-star mr-2"></i>View Plans
                    </button>
                </div>
                
                <!-- Demo Login Toggle -->
                <div class="mt-8 text-center">
                    <button onclick="toggleLoginState()" class="bg-gray-800 text-white px-6 py-2 rounded-lg hover:bg-gray-700 text-sm">
                        <i class="fas fa-toggle-on mr-2"></i>Toggle Login State (Demo)
                    </button>
                    <div class="mt-2 text-xs text-gray-300">
                        Or use the login form: <a href="#" onclick="showPage('login')" class="text-yellow-300 hover:text-yellow-200">Sign In</a> / <a href="#" onclick="showPage('register')" class="text-yellow-300 hover:text-yellow-200">Register</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold text-gray-800 mb-4">Why Choose Our Platform</h2>
                    <p class="text-xl text-gray-600">Experience the future of AI development with our comprehensive platform.</p>
                </div>
                <div class="grid md:grid-cols-3 gap-8">
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-xl hover-scale">
                        <div class="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mb-4">
                            <i class="fas fa-bolt text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-3">Lightning Fast</h3>
                        <p class="text-gray-600">Our AI models deliver instant results with industry-leading speed and accuracy.</p>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-xl hover-scale">
                        <div class="w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center mb-4">
                            <i class="fas fa-expand-arrows-alt text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-3">Scalable Infrastructure</h3>
                        <p class="text-gray-600">Built on robust cloud architecture that scales with your needs.</p>
                    </div>
                    <div class="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-xl hover-scale">
                        <div class="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center mb-4">
                            <i class="fas fa-shield-alt text-white text-xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-3">Enterprise-Grade Security</h3>
                        <p class="text-gray-600">Your data is protected with military-grade encryption and compliance standards.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="py-16 bg-gray-900 text-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid md:grid-cols-4 gap-8 text-center">
                    <div>
                        <div class="text-4xl font-bold text-blue-400 mb-2">99.9%</div>
                        <div class="text-gray-300">Uptime</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold text-purple-400 mb-2">10M+</div>
                        <div class="text-gray-300">API Calls</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold text-green-400 mb-2">50K+</div>
                        <div class="text-gray-300">Developers</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold text-yellow-400 mb-2">24/7</div>
                        <div class="text-gray-300">Support</div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<!-- Login Page -->
<div id="login" class="page">
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <div class="mx-auto h-12 w-12 text-center">
                    <i class="fas fa-robot text-4xl text-blue-600"></i>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">Sign in to your account</h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Or
                    <a href="#" onclick="showPage('register')" class="font-medium text-blue-600 hover:text-blue-500"> register a new account</a>
                </p>
            </div>
            <form class="mt-8 space-y-6">
                <div class="rounded-md shadow-sm -space-y-px">
                    <div>
                        <label for="email" class="sr-only">Email address</label>
                        <input id="email" name="email" type="email" required class="relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Email address">
                    </div>
                    <div>
                        <label for="password" class="sr-only">Password</label>
                        <input id="password" name="password" type="password" required class="relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Password">
                    </div>
                </div>

                <div class="text-center">
                    <a href="#" onclick="showPage('forgot-password')" class="font-medium text-blue-600 hover:text-blue-500 text-sm">Forgot your password?</a>
                </div>

                <div>
                    <button type="button" onclick="performLogin()" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-lock text-blue-500 group-hover:text-blue-400"></i>
                        </span>
                        Sign in
                    </button>
                </div>

                <div class="mt-6">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-gray-50 text-gray-500">Or continue with</span>
                        </div>
                    </div>

                    <div class="mt-6 grid grid-cols-2 gap-3">
                        <button class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i class="fab fa-google text-red-500"></i>
                        </button>
                        <button class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i class="fab fa-github text-gray-900"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Register Page -->
<div id="register" class="page">
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <div class="mx-auto h-12 w-12 text-center">
                    <i class="fas fa-robot text-4xl text-blue-600"></i>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">Create your account</h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Or
                    <a href="#" onclick="showPage('login')" class="font-medium text-blue-600 hover:text-blue-500"> sign in to your existing account</a>
                </p>
            </div>
            <form class="mt-8 space-y-6">
                <div class="space-y-4">
                    <div>
                        <label for="firstName" class="block text-sm font-medium text-gray-700">First Name</label>
                        <input id="firstName" name="firstName" type="text" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="John">
                    </div>
                    <div>
                        <label for="lastName" class="block text-sm font-medium text-gray-700">Last Name</label>
                        <input id="lastName" name="lastName" type="text" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Doe">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email address</label>
                        <input id="email" name="email" type="email" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                        <input id="password" name="password" type="password" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="••••••••">
                    </div>
                    <div>
                        <label for="confirmPassword" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                        <input id="confirmPassword" name="confirmPassword" type="password" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="••••••••">
                    </div>
                </div>

                <div class="flex items-center">
                    <input id="terms" name="terms" type="checkbox" required class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="terms" class="ml-2 block text-sm text-gray-900">
                        I agree to the <a href="#" onclick="showPage('terms')" class="text-blue-600 hover:text-blue-500">Terms of Service</a> and <a href="#" onclick="showPage('privacy')" class="text-blue-600 hover:text-blue-500">Privacy Policy</a>
                    </label>
                </div>

                <div>
                    <button type="button" onclick="performRegister()" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-user-plus text-blue-500 group-hover:text-blue-400"></i>
                        </span>
                        Create Account
                    </button>
                </div>

                <div class="mt-6">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-gray-50 text-gray-500">Or continue with</span>
                        </div>
                    </div>

                    <div class="mt-6 grid grid-cols-2 gap-3">
                        <button type="button" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i class="fab fa-google text-red-500"></i>
                        </button>
                        <button type="button" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i class="fab fa-github text-gray-900"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Forgot Password Page -->
<div id="forgot-password" class="page">
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <div class="mx-auto h-12 w-12 text-center">
                    <i class="fas fa-key text-4xl text-blue-600"></i>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">Forgot your password?</h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    No worries! Enter your email address and we'll send you a link to reset your password.
                </p>
            </div>
            <form class="mt-8 space-y-6">
                <div>
                    <label for="resetEmail" class="block text-sm font-medium text-gray-700">Email address</label>
                    <input id="resetEmail" name="resetEmail" type="email" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="<EMAIL>">
                </div>

                <div>
                    <button type="button" onclick="sendResetEmail()" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-paper-plane text-blue-500 group-hover:text-blue-400"></i>
                        </span>
                        Send Reset Link
                    </button>
                </div>

                <div class="text-center">
                    <a href="#" onclick="showPage('login')" class="font-medium text-blue-600 hover:text-blue-500">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Sign In
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reset Password Page -->
<div id="reset-password" class="page">
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <div class="mx-auto h-12 w-12 text-center">
                    <i class="fas fa-lock text-4xl text-blue-600"></i>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">Reset your password</h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Enter your new password below
                </p>
            </div>
            <form class="mt-8 space-y-6">
                <div class="space-y-4">
                    <div>
                        <label for="newPassword" class="block text-sm font-medium text-gray-700">New Password</label>
                        <input id="newPassword" name="newPassword" type="password" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="••••••••">
                    </div>
                    <div>
                        <label for="confirmNewPassword" class="block text-sm font-medium text-gray-700">Confirm New Password</label>
                        <input id="confirmNewPassword" name="confirmNewPassword" type="password" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="••••••••">
                    </div>
                </div>

                <!-- Password Requirements -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Password requirements:</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            At least 8 characters
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            At least one uppercase letter
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            At least one number
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            At least one special character
                        </li>
                    </ul>
                </div>

                <div>
                    <button type="button" onclick="performPasswordReset()" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-check text-blue-500 group-hover:text-blue-400"></i>
                        </span>
                        Update Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Dashboard Page -->
<div id="dashboard" class="page">
    <div class="pt-16 min-h-screen bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Dashboard Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Welcome back, John!</h1>
                <p class="text-gray-600">Here's what's happening with your AI projects today.</p>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-tasks text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Tasks</p>
                            <p class="text-2xl font-bold text-gray-900">1,247</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-check-circle text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Completed</p>
                            <p class="text-2xl font-bold text-gray-900">1,180</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-clock text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Processing</p>
                            <p class="text-2xl font-bold text-gray-900">42</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                            <i class="fas fa-chart-line text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">API Usage</p>
                            <p class="text-2xl font-bold text-gray-900">78%</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow mb-8">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-900">Quick Actions</h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <button onclick="showPage('tools')" class="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors text-center">
                            <i class="fas fa-image text-2xl text-blue-600 mb-2"></i>
                            <p class="text-sm font-medium text-gray-700">Generate Image</p>
                        </button>
                        <button onclick="showPage('tools')" class="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors text-center">
                            <i class="fas fa-video text-2xl text-purple-600 mb-2"></i>
                            <p class="text-sm font-medium text-gray-700">Create Video</p>
                        </button>
                        <button onclick="showPage('tools')" class="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors text-center">
                            <i class="fas fa-volume-up text-2xl text-green-600 mb-2"></i>
                            <p class="text-sm font-medium text-gray-700">Generate Audio</p>
                        </button>
                        <button onclick="showPage('tools')" class="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-orange-500 hover:bg-orange-50 transition-colors text-center">
                            <i class="fas fa-cube text-2xl text-orange-600 mb-2"></i>
                            <p class="text-sm font-medium text-gray-700">3D Model</p>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Recent Tasks -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h2 class="text-xl font-bold text-gray-900">Recent Tasks</h2>
                        <button onclick="showPage('tasks')" class="text-blue-600 hover:text-blue-800">View All</button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-image text-blue-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="font-medium text-gray-900">Mountain Landscape</p>
                                    <p class="text-sm text-gray-500">Flux-Dev • 2 hours ago</p>
                                </div>
                            </div>
                            <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Completed</span>
                        </div>
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-video text-purple-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="font-medium text-gray-900">Product Demo Video</p>
                                    <p class="text-sm text-gray-500">Veo2-T2V • 5 minutes ago</p>
                                </div>
                            </div>
                            <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">Processing</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AI Tools Page -->
<div id="tools" class="page">
    <div class="pt-16 min-h-screen bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Tools Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">AI Tools</h1>
                <p class="text-gray-600">Choose from our collection of powerful AI models to bring your ideas to life.</p>
            </div>

            <!-- Filter Tabs -->
            <div class="mb-8">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8">
                        <button class="tool-tab active py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600" data-category="all">All Tools</button>
                        <button class="tool-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-category="image">Image Generation</button>
                        <button class="tool-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-category="video">Video Generation</button>
                        <button class="tool-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-category="audio">Audio Generation</button>
                        <button class="tool-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-category="3d">3D Generation</button>
                    </nav>
                </div>
            </div>

            <!-- Tools Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="tools-grid">
                <!-- Image Generation Tools -->
                <div class="tool-card bg-white rounded-lg shadow-md overflow-hidden hover-scale" data-category="image">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-image text-blue-600 text-xl"></i>
                            </div>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Popular</span>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2">Flux-Dev</h3>
                        <p class="text-gray-600 text-sm mb-4">Advanced text-to-image generation with superior quality and detail.</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">1 credit per image</span>
                            <button onclick="openToolModal('flux-dev')" class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700">Use Tool</button>
                        </div>
                    </div>
                </div>

                <div class="tool-card bg-white rounded-lg shadow-md overflow-hidden hover-scale" data-category="image">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-palette text-purple-600 text-xl"></i>
                            </div>
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Fast</span>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2">SDXL</h3>
                        <p class="text-gray-600 text-sm mb-4">Stable Diffusion XL for high-resolution image generation.</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">0.5 credits per image</span>
                            <button onclick="openToolModal('sdxl')" class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700">Use Tool</button>
                        </div>
                    </div>
                </div>

                <!-- Video Generation Tools -->
                <div class="tool-card bg-white rounded-lg shadow-md overflow-hidden hover-scale" data-category="video">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-video text-red-600 text-xl"></i>
                            </div>
                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">Premium</span>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2">Veo2-T2V</h3>
                        <p class="text-gray-600 text-sm mb-4">Google's advanced text-to-video generation model.</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">5 credits per video</span>
                            <button onclick="openToolModal('veo2')" class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700">Use Tool</button>
                        </div>
                    </div>
                </div>

                <!-- Audio Generation Tools -->
                <div class="tool-card bg-white rounded-lg shadow-md overflow-hidden hover-scale" data-category="audio">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-microphone text-green-600 text-xl"></i>
                            </div>
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">New</span>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2">DIA-TTS</h3>
                        <p class="text-gray-600 text-sm mb-4">High-quality text-to-speech synthesis.</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">1 credit per minute</span>
                            <button onclick="openToolModal('dia-tts')" class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700">Use Tool</button>
                        </div>
                    </div>
                </div>

                <!-- 3D Generation Tools -->
                <div class="tool-card bg-white rounded-lg shadow-md overflow-hidden hover-scale" data-category="3d">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-cube text-orange-600 text-xl"></i>
                            </div>
                            <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">Beta</span>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2">Hunyuan3D-v2</h3>
                        <p class="text-gray-600 text-sm mb-4">Generate 3D models from text descriptions.</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">3 credits per model</span>
                            <button onclick="openToolModal('hunyuan3d')" class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700">Use Tool</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tasks Page -->
<div id="tasks" class="page">
    <div class="pt-16 min-h-screen bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Tasks Header -->
            <div class="flex justify-between items-center mb-8">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Task History</h1>
                    <p class="text-gray-600">View and manage your AI generation tasks.</p>
                </div>
                <div class="flex space-x-4">
                    <select class="border border-gray-300 rounded-lg px-3 py-2">
                        <option>All Status</option>
                        <option>Completed</option>
                        <option>Processing</option>
                        <option>Failed</option>
                    </select>
                    <select class="border border-gray-300 rounded-lg px-3 py-2">
                        <option>All Types</option>
                        <option>Image</option>
                        <option>Video</option>
                        <option>Audio</option>
                    </select>
                </div>
            </div>

            <!-- Tasks List -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Recent Tasks</h2>
                </div>
                <div class="divide-y divide-gray-200">
                    <div class="p-6 hover:bg-gray-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="flex-shrink-0">
                                    <img class="h-16 w-16 rounded-lg object-cover" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23ddd'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23999'%3EImage%3C/text%3E%3C/svg%3E" alt="Generated image">
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900">Mountain Landscape at Sunset</h3>
                                    <p class="text-sm text-gray-500">Generated with Flux-Dev • Created 2 hours ago</p>
                                    <p class="text-sm text-gray-600 mt-1">Prompt: "Beautiful mountain landscape at sunset with dramatic clouds..."</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Completed</span>
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-800"><i class="fas fa-download"></i></button>
                                    <button class="text-gray-600 hover:text-gray-800"><i class="fas fa-share"></i></button>
                                    <button class="text-red-600 hover:text-red-800"><i class="fas fa-trash"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 hover:bg-gray-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="flex-shrink-0">
                                    <div class="h-16 w-16 rounded-lg bg-yellow-100 flex items-center justify-center">
                                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                                    </div>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900">Product Demo Video</h3>
                                    <p class="text-sm text-gray-500">Generating with Veo2-T2V • Started 5 minutes ago</p>
                                    <p class="text-sm text-gray-600 mt-1">Prompt: "Professional product demonstration video showcasing..."</p>
                                    <div class="mt-2">
                                        <div class="bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-600 h-2 rounded-full" style="width: 65%"></div>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">Processing... 65% complete</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">Processing</span>
                                <div class="flex space-x-2">
                                    <button class="text-red-600 hover:text-red-800"><i class="fas fa-stop"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 hover:bg-gray-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="flex-shrink-0">
                                    <div class="h-16 w-16 rounded-lg bg-red-100 flex items-center justify-center">
                                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                                    </div>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900">Abstract Art Piece</h3>
                                    <p class="text-sm text-gray-500">Failed with SDXL • 1 day ago</p>
                                    <p class="text-sm text-gray-600 mt-1">Prompt: "Abstract colorful art piece with geometric shapes..."</p>
                                    <p class="text-sm text-red-600 mt-1">Error: Content policy violation detected</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">Failed</span>
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-800"><i class="fas fa-redo"></i></button>
                                    <button class="text-red-600 hover:text-red-800"><i class="fas fa-trash"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex justify-between items-center">
                        <p class="text-sm text-gray-500">Showing 1 to 10 of 247 results</p>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">Previous</button>
                            <button class="px-3 py-1 bg-blue-600 text-white rounded text-sm">1</button>
                            <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">2</button>
                            <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">3</button>
                            <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">Next</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pricing Page -->
<div id="pricing" class="page">
    <div class="pt-16 min-h-screen bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Pricing Header -->
            <div class="text-center mb-16">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
                <p class="text-xl text-gray-600 mb-8">Scale your AI projects with flexible pricing that grows with you.</p>
                
                <!-- Billing Toggle -->
                <div class="flex items-center justify-center mb-8">
                    <span class="text-sm text-gray-500 mr-3">Monthly</span>
                    <button class="relative inline-flex items-center h-6 rounded-full w-11 bg-blue-600 transition-colors">
                        <span class="sr-only">Enable yearly billing</span>
                        <span class="inline-block w-4 h-4 transform bg-white rounded-full transition-transform translate-x-6"></span>
                    </button>
                    <span class="text-sm text-gray-500 ml-3">Yearly</span>
                    <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">20% off</span>
                </div>
            </div>

            <!-- Pricing Cards -->
            <div class="grid md:grid-cols-3 gap-8 mb-16">
                <!-- Starter Plan -->
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <div class="text-center">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Starter</h3>
                        <p class="text-gray-600 mb-4">Perfect for individuals getting started</p>
                        <div class="mb-6">
                            <span class="text-4xl font-bold text-gray-900">$9</span>
                            <span class="text-gray-500">/month</span>
                        </div>
                        <button class="w-full bg-gray-200 text-gray-800 py-3 px-6 rounded-lg font-medium hover:bg-gray-300 transition-colors">
                            Start Free Trial
                        </button>
                    </div>
                    <ul class="mt-8 space-y-4">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">100 AI generations/month</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Basic image models</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Standard support</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">1080p output quality</span>
                        </li>
                    </ul>
                </div>

                <!-- Pro Plan -->
                <div class="bg-white rounded-lg shadow-lg p-8 border-2 border-blue-500 relative">
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">Most Popular</span>
                    </div>
                    <div class="text-center">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Pro</h3>
                        <p class="text-gray-600 mb-4">Best for growing businesses</p>
                        <div class="mb-6">
                            <span class="text-4xl font-bold text-gray-900">$29</span>
                            <span class="text-gray-500">/month</span>
                        </div>
                        <button class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                            Start Free Trial
                        </button>
                    </div>
                    <ul class="mt-8 space-y-4">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">1,000 AI generations/month</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">All AI models available</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Priority support</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">4K output quality</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">API access</span>
                        </li>
                    </ul>
                </div>

                <!-- Enterprise Plan -->
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <div class="text-center">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Enterprise</h3>
                        <p class="text-gray-600 mb-4">For large-scale operations</p>
                        <div class="mb-6">
                            <span class="text-4xl font-bold text-gray-900">$99</span>
                            <span class="text-gray-500">/month</span>
                        </div>
                        <button class="w-full bg-gray-900 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-800 transition-colors">
                            Contact Sales
                        </button>
                    </div>
                    <ul class="mt-8 space-y-4">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Unlimited generations</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Custom model training</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">24/7 dedicated support</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">8K output quality</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">Advanced API features</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span class="text-gray-700">On-premise deployment</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Usage-Based Pricing -->
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">Pay-as-you-go Pricing</h2>
                    <p class="text-gray-600">No subscription needed. Pay only for what you use.</p>
                </div>
                <div class="grid md:grid-cols-4 gap-6">
                    <div class="text-center p-4 border border-gray-200 rounded-lg">
                        <i class="fas fa-image text-blue-600 text-2xl mb-3"></i>
                        <h4 class="font-medium text-gray-900 mb-2">Image Generation</h4>
                        <p class="text-2xl font-bold text-gray-900">$0.05</p>
                        <p class="text-sm text-gray-500">per image</p>
                    </div>
                    <div class="text-center p-4 border border-gray-200 rounded-lg">
                        <i class="fas fa-video text-purple-600 text-2xl mb-3"></i>
                        <h4 class="font-medium text-gray-900 mb-2">Video Generation</h4>
                        <p class="text-2xl font-bold text-gray-900">$0.25</p>
                        <p class="text-sm text-gray-500">per second</p>
                    </div>
                    <div class="text-center p-4 border border-gray-200 rounded-lg">
                        <i class="fas fa-microphone text-green-600 text-2xl mb-3"></i>
                        <h4 class="font-medium text-gray-900 mb-2">Audio Generation</h4>
                        <p class="text-2xl font-bold text-gray-900">$0.10</p>
                        <p class="text-sm text-gray-500">per minute</p>
                    </div>
                    <div class="text-center p-4 border border-gray-200 rounded-lg">
                        <i class="fas fa-cube text-orange-600 text-2xl mb-3"></i>
                        <h4 class="font-medium text-gray-900 mb-2">3D Generation</h4>
                        <p class="text-2xl font-bold text-gray-900">$0.50</p>
                        <p class="text-sm text-gray-500">per model</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Admin Page -->
<div id="admin" class="page">
    <div class="pt-16 min-h-screen bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Admin Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
                <p class="text-gray-600">Manage users, permissions, and system settings.</p>
            </div>

            <!-- Admin Navigation -->
            <div class="mb-8">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8">
                        <button class="admin-tab active py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600" data-section="overview">Overview</button>
                        <button class="admin-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-section="users">Users</button>
                        <button class="admin-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-section="billable-metrics">Billable Metrics</button>
                        <button class="admin-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-section="subscription-plans">Subscription Plans</button>
                        <button class="admin-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-section="permissions">Roles & Permissions</button>
                        <button class="admin-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-section="billing">Billing</button>
                        <button class="admin-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-section="system">System</button>
                    </nav>
                </div>
            </div>

            <!-- Admin Overview -->
            <div id="admin-overview" class="admin-section">
                <!-- System Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-users text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Users</p>
                                <p class="text-2xl font-bold text-gray-900">12,847</p>
                                <p class="text-sm text-green-600">+5.2% from last month</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-chart-line text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">API Calls</p>
                                <p class="text-2xl font-bold text-gray-900">2.4M</p>
                                <p class="text-sm text-green-600">+12.8% from last month</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                <i class="fas fa-dollar-sign text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Revenue</p>
                                <p class="text-2xl font-bold text-gray-900">$48,590</p>
                                <p class="text-sm text-green-600">+8.1% from last month</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-red-100 text-red-600">
                                <i class="fas fa-exclamation-triangle text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Error Rate</p>
                                <p class="text-2xl font-bold text-gray-900">0.2%</p>
                                <p class="text-sm text-red-600">+0.1% from last month</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Recent User Activity</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user-plus text-green-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">New user registration</p>
                                        <p class="text-xs text-gray-500"><EMAIL> • 5 minutes ago</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-credit-card text-blue-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Subscription upgrade</p>
                                        <p class="text-xs text-gray-500"><EMAIL> • 15 minutes ago</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-robot text-purple-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">High API usage alert</p>
                                        <p class="text-xs text-gray-500"><EMAIL> • 1 hour ago</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">System Health</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-700">API Response Time</span>
                                    <span class="text-sm text-green-600">245ms</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 85%"></div>
                                </div>
                                
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-700">Database Performance</span>
                                    <span class="text-sm text-green-600">98%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 98%"></div>
                                </div>
                                
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-700">Cache Hit Rate</span>
                                    <span class="text-sm text-blue-600">94%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 94%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Management -->
            <div id="admin-users" class="admin-section hidden">
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">User Management</h3>
                            <button onclick="openUserModal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                <i class="fas fa-plus mr-2"></i>Add User
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <!-- Search and Filters -->
                        <div class="flex justify-between items-center mb-6">
                            <div class="flex space-x-4">
                                <input type="text" placeholder="Search users..." class="border border-gray-300 rounded-lg px-4 py-2 w-64">
                                <select class="border border-gray-300 rounded-lg px-3 py-2">
                                    <option>All Roles</option>
                                    <option>Admin</option>
                                    <option>User</option>
                                    <option>Enterprise</option>
                                </select>
                            </div>
                        </div>

                        <!-- Users Table -->
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Active</th>
                                        <th class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <i class="fas fa-user text-gray-600"></i>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">John Doe</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">User</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Pro</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Active</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2 hours ago</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button onclick="editUser('<EMAIL>')" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                                            <button onclick="deactivateUser('<EMAIL>')" class="text-red-600 hover:text-red-900">Deactivate</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <i class="fas fa-user text-gray-600"></i>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">Alice Smith</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">Admin</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Enterprise</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Active</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1 hour ago</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button onclick="editUser('<EMAIL>')" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                                            <button onclick="deactivateUser('<EMAIL>')" class="text-red-600 hover:text-red-900">Deactivate</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <i class="fas fa-user text-gray-600"></i>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">Bob Johnson</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">User</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Starter</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Pending</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1 day ago</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button onclick="editUser('<EMAIL>')" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                                            <button onclick="deactivateUser('<EMAIL>')" class="text-red-600 hover:text-red-900">Deactivate</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Billable Metrics Management -->
            <div id="admin-billable-metrics" class="admin-section hidden">
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">Billable Metrics</h3>
                            <button onclick="openMetricModal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                <i class="fas fa-plus mr-2"></i>Add Metric
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <!-- Metrics Table -->
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Metric Name</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">Image Generation</div>
                                            <div class="text-sm text-gray-500">Per image generated</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">img_gen</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">Per Unit</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$0.05</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Active</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button onclick="editMetric('img_gen')" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                                            <button onclick="deleteMetric('img_gen')" class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">Video Generation</div>
                                            <div class="text-sm text-gray-500">Per second of video</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">vid_gen</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">Per Second</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$0.25</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Active</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button onclick="editMetric('vid_gen')" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                                            <button onclick="deleteMetric('vid_gen')" class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">API Calls</div>
                                            <div class="text-sm text-gray-500">Per API request</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">api_call</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Per Unit</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$0.01</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Active</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button onclick="editMetric('api_call')" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                                            <button onclick="deleteMetric('api_call')" class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Plans Management -->
            <div id="admin-subscription-plans" class="admin-section hidden">
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">Subscription Plans</h3>
                            <button onclick="openPlanModal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                <i class="fas fa-plus mr-2"></i>Add Plan
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <!-- Plans Grid -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- Starter Plan -->
                            <div class="border border-gray-200 rounded-lg p-6">
                                <div class="flex justify-between items-start mb-4">
                                    <h4 class="text-lg font-bold text-gray-900">Starter</h4>
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Active</span>
                                </div>
                                <div class="mb-4">
                                    <span class="text-3xl font-bold text-gray-900">$9</span>
                                    <span class="text-gray-500">/month</span>
                                </div>
                                <ul class="space-y-2 mb-6">
                                    <li class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        100 AI generations/month
                                    </li>
                                    <li class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        Basic image models
                                    </li>
                                    <li class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        Standard support
                                    </li>
                                </ul>
                                <div class="flex space-x-2">
                                    <button onclick="editPlan('starter')" class="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700">Edit</button>
                                    <button onclick="deletePlan('starter')" class="bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700">Delete</button>
                                </div>
                            </div>

                            <!-- Pro Plan -->
                            <div class="border-2 border-blue-500 rounded-lg p-6 relative">
                                <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                    <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-xs">Popular</span>
                                </div>
                                <div class="flex justify-between items-start mb-4">
                                    <h4 class="text-lg font-bold text-gray-900">Pro</h4>
                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Active</span>
                                </div>
                                <div class="mb-4">
                                    <span class="text-3xl font-bold text-gray-900">$29</span>
                                    <span class="text-gray-500">/month</span>
                                </div>
                                <ul class="space-y-2 mb-6">
                                    <li class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        1,000 AI generations/month
                                    </li>
                                    <li class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        All AI models available
                                    </li>
                                    <li class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        Priority support
                                    </li>
                                    <li class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        API access
                                    </li>
                                </ul>
                                <div class="flex space-x-2">
                                    <button onclick="editPlan('pro')" class="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700">Edit</button>
                                    <button onclick="deletePlan('pro')" class="bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700">Delete</button>
                                </div>
                            </div>

                            <!-- Enterprise Plan -->
                            <div class="border border-gray-200 rounded-lg p-6">
                                <div class="flex justify-between items-start mb-4">
                                    <h4 class="text-lg font-bold text-gray-900">Enterprise</h4>
                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Active</span>
                                </div>
                                <div class="mb-4">
                                    <span class="text-3xl font-bold text-gray-900">$99</span>
                                    <span class="text-gray-500">/month</span>
                                </div>
                                <ul class="space-y-2 mb-6">
                                    <li class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        Unlimited generations
                                    </li>
                                    <li class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        Custom model training
                                    </li>
                                    <li class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        24/7 dedicated support
                                    </li>
                                    <li class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        On-premise deployment
                                    </li>
                                </ul>
                                <div class="flex space-x-2">
                                    <button onclick="editPlan('enterprise')" class="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700">Edit</button>
                                    <button onclick="deletePlan('enterprise')" class="bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700">Delete</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Permissions Management -->
            <div id="admin-permissions" class="admin-section hidden">
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">Roles & Permissions</h3>
                            <button onclick="openRoleModal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                <i class="fas fa-plus mr-2"></i>Add Role
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <!-- Roles Table -->
                        <div class="overflow-x-auto mb-8">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role Name</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Permissions</th>
                                        <th class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">Super Admin</div>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Full system access and control</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2 users</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">All permissions</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button onclick="editRole('super_admin')" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                                            <button onclick="deleteRole('super_admin')" class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">Admin</div>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Manage users and content</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">5 users</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">User management, Content moderation</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button onclick="editRole('admin')" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                                            <button onclick="deleteRole('admin')" class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">User</div>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Standard user access</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1,247 users</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">AI generation, Profile management</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button onclick="editRole('user')" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                                            <button onclick="deleteRole('user')" class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">Enterprise</div>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Enterprise-level access with advanced features</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">420 users</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">Advanced AI features, API access, Priority support</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button onclick="editRole('enterprise')" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                                            <button onclick="deleteRole('enterprise')" class="text-red-600 hover:text-red-900">Delete</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Permissions Matrix -->
                        <div class="border-t border-gray-200 pt-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">Permissions Matrix</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Permission</th>
                                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Super Admin</th>
                                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Admin</th>
                                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Enterprise</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-6 py-4 text-sm font-medium text-gray-900">User Management</td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-check text-green-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-check text-green-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-times text-red-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-times text-red-500"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 text-sm font-medium text-gray-900">AI Generation</td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-check text-green-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-check text-green-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-check text-green-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-check text-green-500"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 text-sm font-medium text-gray-900">Billing Management</td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-check text-green-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-times text-red-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-times text-red-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-check text-green-500"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 text-sm font-medium text-gray-900">System Settings</td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-check text-green-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-times text-red-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-times text-red-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-times text-red-500"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 text-sm font-medium text-gray-900">API Access</td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-check text-green-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-check text-green-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-times text-red-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-check text-green-500"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 text-sm font-medium text-gray-900">Priority Support</td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-check text-green-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-times text-red-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-times text-red-500"></i>
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <i class="fas fa-check text-green-500"></i>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Billing Management -->
            <div id="admin-billing" class="admin-section hidden">
                <div class="space-y-8">
                    <!-- Revenue Overview -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Revenue Overview</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-green-600 mb-2">$48,590</div>
                                    <div class="text-sm text-gray-600">This Month</div>
                                    <div class="text-xs text-green-600 mt-1">+8.1% from last month</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-blue-600 mb-2">$156,420</div>
                                    <div class="text-sm text-gray-600">This Quarter</div>
                                    <div class="text-xs text-blue-600 mt-1">+12.3% from last quarter</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-purple-600 mb-2">$892,340</div>
                                    <div class="text-sm text-gray-600">This Year</div>
                                    <div class="text-xs text-purple-600 mt-1">+18.7% from last year</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-orange-600 mb-2">2,847</div>
                                    <div class="text-sm text-gray-600">Active Subscriptions</div>
                                    <div class="text-xs text-orange-600 mt-1">+5.2% from last month</div>
                                </div>
                            </div>

                            <!-- Revenue Chart Placeholder -->
                            <div class="bg-gray-50 rounded-lg p-8 text-center">
                                <i class="fas fa-chart-line text-4xl text-gray-400 mb-4"></i>
                                <h4 class="text-lg font-medium text-gray-700 mb-2">Revenue Chart</h4>
                                <p class="text-gray-500">Monthly revenue trends and growth analytics</p>
                            </div>
                        </div>
                    </div>

                    <!-- Subscription Analytics -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Subscription Analytics</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="text-center p-4 border border-gray-200 rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600 mb-2">1,247</div>
                                    <div class="text-sm text-gray-600">Starter Plans</div>
                                    <div class="text-xs text-gray-500 mt-1">43.8% of total</div>
                                </div>
                                <div class="text-center p-4 border border-gray-200 rounded-lg">
                                    <div class="text-2xl font-bold text-green-600 mb-2">1,180</div>
                                    <div class="text-sm text-gray-600">Pro Plans</div>
                                    <div class="text-xs text-gray-500 mt-1">41.4% of total</div>
                                </div>
                                <div class="text-center p-4 border border-gray-200 rounded-lg">
                                    <div class="text-2xl font-bold text-purple-600 mb-2">420</div>
                                    <div class="text-sm text-gray-600">Enterprise Plans</div>
                                    <div class="text-xs text-gray-500 mt-1">14.8% of total</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Transactions -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Recent Transactions</h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <i class="fas fa-user text-gray-600 text-sm"></i>
                                                </div>
                                                <div class="ml-3">
                                                    <div class="text-sm font-medium text-gray-900">John Doe</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Pro Plan</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$29.00</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Paid</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Mar 15, 2024</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <i class="fas fa-user text-gray-600 text-sm"></i>
                                                </div>
                                                <div class="ml-3">
                                                    <div class="text-sm font-medium text-gray-900">Alice Smith</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Enterprise Plan</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$99.00</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Paid</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Mar 14, 2024</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <i class="fas fa-user text-gray-600 text-sm"></i>
                                                </div>
                                                <div class="ml-3">
                                                    <div class="text-sm font-medium text-gray-900">Bob Johnson</div>
                                                    <div class="text-sm text-gray-500"><EMAIL></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Starter Plan</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$9.00</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Pending</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Mar 13, 2024</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Management -->
            <div id="admin-system" class="admin-section hidden">
                <div class="space-y-8">
                    <!-- System Health -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">System Health</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                                <div class="text-center p-4 border border-gray-200 rounded-lg">
                                    <div class="text-2xl font-bold text-green-600 mb-2">99.9%</div>
                                    <div class="text-sm text-gray-600">Uptime</div>
                                    <div class="text-xs text-green-600 mt-1">Last 30 days</div>
                                </div>
                                <div class="text-center p-4 border border-gray-200 rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600 mb-2">245ms</div>
                                    <div class="text-sm text-gray-600">Avg Response Time</div>
                                    <div class="text-xs text-blue-600 mt-1">API endpoints</div>
                                </div>
                                <div class="text-center p-4 border border-gray-200 rounded-lg">
                                    <div class="text-2xl font-bold text-purple-600 mb-2">94%</div>
                                    <div class="text-sm text-gray-600">Cache Hit Rate</div>
                                    <div class="text-xs text-purple-600 mt-1">Redis cache</div>
                                </div>
                                <div class="text-center p-4 border border-gray-200 rounded-lg">
                                    <div class="text-2xl font-bold text-orange-600 mb-2">78%</div>
                                    <div class="text-sm text-gray-600">CPU Usage</div>
                                    <div class="text-xs text-orange-600 mt-1">Server load</div>
                                </div>
                            </div>

                            <!-- System Status -->
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                        <div>
                                            <h4 class="text-sm font-medium text-green-800">API Service</h4>
                                            <p class="text-sm text-green-600">All endpoints operational</p>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Healthy</span>
                                </div>
                                <div class="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                        <div>
                                            <h4 class="text-sm font-medium text-green-800">Database</h4>
                                            <p class="text-sm text-green-600">PostgreSQL running normally</p>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Healthy</span>
                                </div>
                                <div class="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-exclamation-triangle text-yellow-500 mr-3"></i>
                                        <div>
                                            <h4 class="text-sm font-medium text-yellow-800">AI Model Service</h4>
                                            <p class="text-sm text-yellow-600">High latency detected</p>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Warning</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Settings -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">System Settings</h3>
                        </div>
                        <div class="p-6 space-y-6">
                            <!-- General Settings -->
                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-4">General Settings</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
                                        <input type="text" value="AI Generator Platform" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Site URL</label>
                                        <input type="url" value="https://aigenerator.com" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Default Language</label>
                                        <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                                            <option selected>English</option>
                                            <option>Chinese</option>
                                            <option>Spanish</option>
                                            <option>French</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                                        <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                                            <option selected>UTC</option>
                                            <option>America/New_York</option>
                                            <option>Europe/London</option>
                                            <option>Asia/Shanghai</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Email Settings -->
                            <div class="border-t border-gray-200 pt-6">
                                <h4 class="text-md font-medium text-gray-900 mb-4">Email Settings</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">SMTP Host</label>
                                        <input type="text" value="smtp.gmail.com" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">SMTP Port</label>
                                        <input type="number" value="587" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">From Email</label>
                                        <input type="email" value="<EMAIL>" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">From Name</label>
                                        <input type="text" value="AI Generator" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                </div>
                            </div>

                            <!-- Security Settings -->
                            <div class="border-t border-gray-200 pt-6">
                                <h4 class="text-md font-medium text-gray-900 mb-4">Security Settings</h4>
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h5 class="text-sm font-medium text-gray-700">Two-Factor Authentication</h5>
                                            <p class="text-sm text-gray-500">Require 2FA for all users</p>
                                        </div>
                                        <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 text-sm">
                                            Enable
                                        </button>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h5 class="text-sm font-medium text-gray-700">Rate Limiting</h5>
                                            <p class="text-sm text-gray-500">Limit API requests per minute</p>
                                        </div>
                                        <input type="number" value="100" class="w-20 border border-gray-300 rounded-lg px-3 py-2 text-sm">
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h5 class="text-sm font-medium text-gray-700">Session Timeout</h5>
                                            <p class="text-sm text-gray-500">Auto-logout after inactivity</p>
                                        </div>
                                        <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                                            <option>15 minutes</option>
                                            <option selected>30 minutes</option>
                                            <option>1 hour</option>
                                            <option>24 hours</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Maintenance Mode -->
                            <div class="border-t border-gray-200 pt-6">
                                <h4 class="text-md font-medium text-gray-900 mb-4">Maintenance Mode</h4>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h5 class="text-sm font-medium text-gray-700">Enable Maintenance Mode</h5>
                                        <p class="text-sm text-gray-500">Temporarily disable the platform for maintenance</p>
                                    </div>
                                    <button class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 text-sm">
                                        Enable Maintenance
                                    </button>
                                </div>
                            </div>

                            <div class="flex justify-end pt-6 border-t border-gray-200">
                                <button class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                                    Save Settings
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Logs & Monitoring -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">System Logs</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-info-circle text-blue-500 mr-3"></i>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">User registration successful</p>
                                            <p class="text-xs text-gray-500"><EMAIL> • 2 minutes ago</p>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Info</span>
                                </div>
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-exclamation-triangle text-yellow-500 mr-3"></i>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">High API usage detected</p>
                                            <p class="text-xs text-gray-500">User ID: 12345 • 5 minutes ago</p>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Warning</span>
                                </div>
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-times-circle text-red-500 mr-3"></i>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">Payment processing failed</p>
                                            <p class="text-xs text-gray-500">Transaction ID: TXN-789 • 10 minutes ago</p>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">Error</span>
                                </div>
                            </div>
                            <div class="mt-6 text-center">
                                <button class="text-blue-600 hover:text-blue-800 text-sm">
                                    View All Logs
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Profile Page -->
<div id="profile" class="page">
    <div class="pt-16 min-h-screen bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Profile Header -->
            <div class="bg-white rounded-lg shadow mb-8">
                <div class="px-6 py-8">
                    <div class="flex items-center space-x-6">
                        <div class="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center text-white text-3xl font-bold">
                            JD
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">John Doe</h1>
                            <p class="text-gray-600"><EMAIL></p>
                            <p class="text-sm text-gray-500 mt-1">Pro Plan • Member since March 2024</p>
                        </div>
                        <div class="ml-auto">
                            <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                <i class="fas fa-edit mr-2"></i>Edit Profile
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 mb-2">1,247</div>
                        <div class="text-sm text-gray-600">Total Generations</div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600 mb-2">1,180</div>
                        <div class="text-sm text-gray-600">Successful</div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600 mb-2">78%</div>
                        <div class="text-sm text-gray-600">API Usage</div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-orange-600 mb-2">$89</div>
                        <div class="text-sm text-gray-600">This Month</div>
                    </div>
                </div>
            </div>

            <!-- Profile Tabs -->
            <div class="bg-white rounded-lg shadow">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6">
                        <button class="profile-tab active py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600" data-tab="info">Personal Info</button>
                        <button class="profile-tab py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-tab="activity">Recent Activity</button>
                        <button class="profile-tab py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-tab="preferences">Preferences</button>
                    </nav>
                </div>

                <!-- Personal Info Tab -->
                <div id="profile-info" class="profile-tab-content p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                            <input type="text" value="John Doe" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" value="<EMAIL>" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Company</label>
                            <input type="text" value="Tech Startup Inc." class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                            <input type="tel" value="+****************" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Bio</label>
                            <textarea rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Tell us about yourself...">Product manager passionate about AI and automation.</textarea>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end">
                        <button class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                            Save Changes
                        </button>
                    </div>
                </div>

                <!-- Recent Activity Tab -->
                <div id="profile-activity" class="profile-tab-content p-6 hidden">
                    <div class="space-y-4">
                        <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-image text-blue-600"></i>
                            </div>
                            <div class="flex-1">
                                <p class="font-medium text-gray-900">Generated mountain landscape</p>
                                <p class="text-sm text-gray-500">Using Flux-Dev • 2 hours ago</p>
                            </div>
                            <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Success</span>
                        </div>
                        <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-video text-purple-600"></i>
                            </div>
                            <div class="flex-1">
                                <p class="font-medium text-gray-900">Created product demo video</p>
                                <p class="text-sm text-gray-500">Using Veo2-T2V • 5 hours ago</p>
                            </div>
                            <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Success</span>
                        </div>
                    </div>
                </div>

                <!-- Preferences Tab -->
                <div id="profile-preferences" class="profile-tab-content p-6 hidden">
                    <div class="space-y-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Notification Preferences</h3>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">Email notifications for completed tasks</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">Weekly usage reports</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">Marketing emails</span>
                                </label>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Default Settings</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Preferred Image Style</label>
                                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                        <option>Realistic</option>
                                        <option selected>Artistic</option>
                                        <option>Abstract</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Default Quality</label>
                                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                        <option>Standard</option>
                                        <option selected>High</option>
                                        <option>Ultra</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end">
                        <button class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                            Save Preferences
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Settings Page -->
<div id="settings" class="page">
    <div class="pt-16 min-h-screen bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Account Settings</h1>
                <p class="text-gray-600">Manage your account preferences and security settings.</p>
            </div>

            <!-- Settings Sections -->
            <div class="space-y-8">
                <!-- Security Settings -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-900">Security</h2>
                    </div>
                    <div class="p-6 space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                            <input type="password" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                            <input type="password" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                            <input type="password" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div class="pt-4 border-t border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-900">Two-Factor Authentication</h3>
                                    <p class="text-sm text-gray-500">Add an extra layer of security to your account</p>
                                </div>
                                <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                                    Enable 2FA
                                </button>
                            </div>
                        </div>
                        <div class="flex justify-end">
                            <button class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                                Update Password
                            </button>
                        </div>
                    </div>
                </div>

                <!-- API Settings -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-900">API Access</h2>
                    </div>
                    <div class="p-6">
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                            <div class="flex space-x-2">
                                <input type="text" value="sk-••••••••••••••••••••••••••••••••" readonly class="flex-1 border border-gray-300 rounded-lg px-3 py-2 bg-gray-50">
                                <button class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                                    Copy
                                </button>
                                <button class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                                    Regenerate
                                </button>
                            </div>
                        </div>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div class="flex">
                                <i class="fas fa-exclamation-triangle text-yellow-400 mr-3 mt-1"></i>
                                <div>
                                    <h3 class="text-sm font-medium text-yellow-800">Keep your API key secure</h3>
                                    <p class="text-sm text-yellow-700 mt-1">Your API key provides access to your account. Don't share it or store it in public repositories.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Actions -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-900">Account Actions</h2>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div>
                                <h3 class="text-sm font-medium text-gray-900">Download Your Data</h3>
                                <p class="text-sm text-gray-500">Export all your generated content and account data</p>
                            </div>
                            <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                Download
                            </button>
                        </div>
                        <div class="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
                            <div>
                                <h3 class="text-sm font-medium text-red-900">Delete Account</h3>
                                <p class="text-sm text-red-700">Permanently delete your account and all associated data</p>
                            </div>
                            <button class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                                Delete Account
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Billing Page -->
<div id="billing" class="page">
    <div class="pt-16 min-h-screen bg-gray-50">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Billing & Usage</h1>
                <p class="text-gray-600">Manage your subscription and monitor your usage.</p>
            </div>

            <!-- Current Plan -->
            <div class="bg-white rounded-lg shadow mb-8">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-900">Current Plan</h2>
                </div>
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Pro Plan</h3>
                            <p class="text-gray-600">1,000 AI generations per month</p>
                            <p class="text-sm text-gray-500 mt-1">Next billing date: April 15, 2024</p>
                        </div>
                        <div class="text-right">
                            <div class="text-3xl font-bold text-gray-900">$29<span class="text-lg text-gray-500">/month</span></div>
                            <button class="mt-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                Upgrade Plan
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Usage This Month -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-900">Usage This Month</h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-700">API Calls</span>
                                    <span class="text-sm text-gray-500">780 / 1,000</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 78%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-700">Storage Used</span>
                                    <span class="text-sm text-gray-500">2.1 GB / 10 GB</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 21%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-700">Bandwidth</span>
                                    <span class="text-sm text-gray-500">45 GB / 100 GB</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-purple-600 h-2 rounded-full" style="width: 45%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-bold text-gray-900">Payment Method</h2>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center space-x-4 mb-4">
                            <div class="w-12 h-8 bg-blue-600 rounded flex items-center justify-center text-white text-xs font-bold">
                                VISA
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">•••• •••• •••• 4242</p>
                                <p class="text-sm text-gray-500">Expires 12/26</p>
                            </div>
                            <div class="ml-auto">
                                <button class="text-blue-600 hover:text-blue-800 text-sm">Edit</button>
                            </div>
                        </div>
                        <button class="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200">
                            <i class="fas fa-plus mr-2"></i>Add Payment Method
                        </button>
                    </div>
                </div>
            </div>

            <!-- Billing History -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-900">Billing History</h2>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Mar 15, 2024</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Pro Plan - Monthly</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$29.00</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Paid</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-900">Download</button>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Feb 15, 2024</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Pro Plan - Monthly</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$29.00</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Paid</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-900">Download</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Metric Modal -->
<div id="metricModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-8 max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h2 id="metricModalTitle" class="text-2xl font-bold text-gray-900">Add Billable Metric</h2>
            <button onclick="closeMetricModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form id="metricForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Metric Name</label>
                    <input type="text" id="metricName" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., Image Generation">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Metric Code</label>
                    <input type="text" id="metricCode" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., img_gen">
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Metric Type</label>
                    <select id="metricType" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="per_unit">Per Unit</option>
                        <option value="per_second">Per Second</option>
                        <option value="per_minute">Per Minute</option>
                        <option value="per_hour">Per Hour</option>
                        <option value="per_day">Per Day</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Price (USD)</label>
                    <input type="number" id="metricPrice" step="0.01" min="0" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.05">
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea id="metricDescription" rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Describe what this metric measures..."></textarea>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="metricStatus" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            
            <div class="flex justify-end space-x-4">
                <button type="button" onclick="closeMetricModal()" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    Cancel
                </button>
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    <i class="fas fa-save mr-2"></i>Save Metric
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Add/Edit User Modal -->
<div id="userModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-8 max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h2 id="userModalTitle" class="text-2xl font-bold text-gray-900">Add User</h2>
            <button onclick="closeUserModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form id="userForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                    <input type="text" id="userFirstName" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="John">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                    <input type="text" id="userLastName" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Doe">
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                <input type="email" id="userEmail" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="<EMAIL>">
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                    <select id="userRole" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="user">User</option>
                        <option value="admin">Admin</option>
                        <option value="super_admin">Super Admin</option>
                        <option value="enterprise">Enterprise</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Subscription Plan</label>
                    <select id="userPlan" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="starter">Starter</option>
                        <option value="pro">Pro</option>
                        <option value="enterprise">Enterprise</option>
                    </select>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="userStatus" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="active">Active</option>
                        <option value="pending">Pending</option>
                        <option value="suspended">Suspended</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Company</label>
                    <input type="text" id="userCompany" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Company Name">
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                <textarea id="userNotes" rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Additional notes about this user..."></textarea>
            </div>
            
            <div class="flex justify-end space-x-4">
                <button type="button" onclick="closeUserModal()" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    Cancel
                </button>
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    <i class="fas fa-save mr-2"></i>Save User
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Add/Edit Role Modal -->
<div id="roleModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-8 max-w-4xl w-full mx-4 max-h-screen overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h2 id="roleModalTitle" class="text-2xl font-bold text-gray-900">Add Role</h2>
            <button onclick="closeRoleModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form id="roleForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Role Name</label>
                    <input type="text" id="roleName" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., Content Moderator">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Role Code</label>
                    <input type="text" id="roleCode" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., content_moderator">
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea id="roleDescription" rows="2" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Brief description of this role..."></textarea>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-4">Permissions</label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-3">
                        <h4 class="text-sm font-medium text-gray-900">User Management</h4>
                        <label class="flex items-center">
                            <input type="checkbox" id="perm_user_view" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">View Users</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="perm_user_create" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Create Users</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="perm_user_edit" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Edit Users</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="perm_user_delete" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Delete Users</span>
                        </label>
                    </div>
                    
                    <div class="space-y-3">
                        <h4 class="text-sm font-medium text-gray-900">AI Generation</h4>
                        <label class="flex items-center">
                            <input type="checkbox" id="perm_ai_generate" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Generate AI Content</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="perm_ai_advanced" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Advanced AI Features</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="perm_ai_api" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">API Access</span>
                        </label>
                    </div>
                    
                    <div class="space-y-3">
                        <h4 class="text-sm font-medium text-gray-900">Billing & Finance</h4>
                        <label class="flex items-center">
                            <input type="checkbox" id="perm_billing_view" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">View Billing</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="perm_billing_manage" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Manage Billing</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="perm_billing_refund" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Process Refunds</span>
                        </label>
                    </div>
                    
                    <div class="space-y-3">
                        <h4 class="text-sm font-medium text-gray-900">System Administration</h4>
                        <label class="flex items-center">
                            <input type="checkbox" id="perm_system_view" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">View System Settings</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="perm_system_edit" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Edit System Settings</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="perm_system_logs" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">View System Logs</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end space-x-4">
                <button type="button" onclick="closeRoleModal()" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    Cancel
                </button>
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    <i class="fas fa-save mr-2"></i>Save Role
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Add/Edit Plan Modal -->
<div id="planModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-8 max-w-4xl w-full mx-4 max-h-screen overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h2 id="planModalTitle" class="text-2xl font-bold text-gray-900">Add Subscription Plan</h2>
            <button onclick="closePlanModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form id="planForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Plan Name</label>
                    <input type="text" id="planName" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., Pro">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Plan Code</label>
                    <input type="text" id="planCode" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., pro">
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Price (USD)</label>
                    <input type="number" id="planPrice" step="0.01" min="0" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="29.00">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Billing Cycle</label>
                    <select id="planBillingCycle" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="monthly">Monthly</option>
                        <option value="yearly">Yearly</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="planStatus" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea id="planDescription" rows="2" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Brief description of the plan..."></textarea>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Features</label>
                <div id="planFeatures" class="space-y-2">
                    <div class="flex items-center space-x-2">
                        <input type="text" class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., 1000 AI generations/month">
                        <button type="button" onclick="addFeatureField()" class="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button type="button" onclick="removeFeatureField(this)" class="bg-red-600 text-white px-3 py-2 rounded-lg hover:bg-red-700">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Limits</label>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-xs text-gray-600 mb-1">Image Generations</label>
                        <input type="number" id="planImageLimit" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="1000">
                    </div>
                    <div>
                        <label class="block text-xs text-gray-600 mb-1">Video Generations</label>
                        <input type="number" id="planVideoLimit" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="100">
                    </div>
                    <div>
                        <label class="block text-xs text-gray-600 mb-1">API Calls</label>
                        <input type="number" id="planApiLimit" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="10000">
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end space-x-4">
                <button type="button" onclick="closePlanModal()" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    Cancel
                </button>
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    <i class="fas fa-save mr-2"></i>Save Plan
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Tool Modal -->
<div id="toolModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-8 max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h2 id="modalTitle" class="text-2xl font-bold text-gray-900">AI Tool Configuration</h2>
            <button onclick="closeToolModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form id="toolForm" class="space-y-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Prompt</label>
                <textarea rows="4" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Describe what you want to generate..."></textarea>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Style</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2">
                        <option>Realistic</option>
                        <option>Artistic</option>
                        <option>Abstract</option>
                        <option>Cartoon</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Quality</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2">
                        <option>Standard</option>
                        <option>High</option>
                        <option>Ultra</option>
                    </select>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Advanced Settings</label>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-700">Inference Steps</span>
                        <input type="range" min="1" max="50" value="28" class="w-32">
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-700">Guidance Scale</span>
                        <input type="range" min="1" max="20" value="7" class="w-32">
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end space-x-4">
                <button type="button" onclick="closeToolModal()" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    Cancel
                </button>
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    <i class="fas fa-magic mr-2"></i>Generate
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Login state management
let isLoggedIn = false;

function toggleLoginState() {
    isLoggedIn = !isLoggedIn;
    const body = document.getElementById('mainBody');
    
    if (isLoggedIn) {
        body.classList.add('user-logged-in');
    } else {
        body.classList.remove('user-logged-in');
    }
}

function login() {
    isLoggedIn = true;
    document.getElementById('mainBody').classList.add('user-logged-in');
}

function logout() {
    isLoggedIn = false;
    document.getElementById('mainBody').classList.remove('user-logged-in');
    showPage('home');
    // Close user menu if open
    document.getElementById('userMenu').classList.remove('show');
}

// User menu toggle
function toggleUserMenu() {
    const userMenu = document.getElementById('userMenu');
    userMenu.classList.toggle('show');
}

// Close user menu when clicking outside
document.addEventListener('click', function(event) {
    const userMenu = document.getElementById('userMenu');
    const userMenuButton = event.target.closest('[onclick="toggleUserMenu()"]');
    
    if (!userMenuButton && !userMenu.contains(event.target)) {
        userMenu.classList.remove('show');
    }
});

// Page navigation
function showPage(pageId) {
    // Hide all pages
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });
    
    // Show selected page
    document.getElementById(pageId).classList.add('active');
    
    // Update nav active state
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('text-blue-600');
        link.classList.add('text-gray-700');
    });
    
    // Close user menu
    document.getElementById('userMenu')?.classList.remove('show');
}

// Tool filtering
document.querySelectorAll('.tool-tab').forEach(tab => {
    tab.addEventListener('click', function() {
        // Update active tab
        document.querySelectorAll('.tool-tab').forEach(t => {
            t.classList.remove('active', 'border-blue-500', 'text-blue-600');
            t.classList.add('border-transparent', 'text-gray-500');
        });
        this.classList.add('active', 'border-blue-500', 'text-blue-600');
        this.classList.remove('border-transparent', 'text-gray-500');
        
        // Filter tools
        const category = this.dataset.category;
        document.querySelectorAll('.tool-card').forEach(card => {
            if (category === 'all' || card.dataset.category === category) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    });
});

// Admin tabs
document.querySelectorAll('.admin-tab').forEach(tab => {
    tab.addEventListener('click', function() {
        // Update active tab
        document.querySelectorAll('.admin-tab').forEach(t => {
            t.classList.remove('border-blue-500', 'text-blue-600');
            t.classList.add('border-transparent', 'text-gray-500');
        });
        this.classList.add('border-blue-500', 'text-blue-600');
        this.classList.remove('border-transparent', 'text-gray-500');
        
        // Show corresponding section
        document.querySelectorAll('.admin-section').forEach(section => {
            section.classList.add('hidden');
        });
        document.getElementById('admin-' + this.dataset.section).classList.remove('hidden');
    });
});

// Tool modal
function openToolModal(toolId) {
    document.getElementById('toolModal').classList.remove('hidden');
    document.getElementById('toolModal').classList.add('flex');
    
    // Update modal title based on tool
    const titles = {
        'flux-dev': 'Flux-Dev Configuration',
        'sdxl': 'SDXL Configuration',
        'veo2': 'Veo2-T2V Configuration',
        'dia-tts': 'DIA-TTS Configuration',
        'hunyuan3d': 'Hunyuan3D Configuration'
    };
    document.getElementById('modalTitle').textContent = titles[toolId] || 'AI Tool Configuration';
}

function closeToolModal() {
    document.getElementById('toolModal').classList.add('hidden');
    document.getElementById('toolModal').classList.remove('flex');
}

// Form submission
document.getElementById('toolForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('Task submitted successfully! You can view progress in the Tasks page.');
    closeToolModal();
    showPage('tasks');
});

// Login function
function performLogin() {
    login();
    showPage('dashboard');
}

// Registration function
function performRegister() {
    // Basic validation
    const firstName = document.getElementById('firstName').value;
    const lastName = document.getElementById('lastName').value;
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const terms = document.getElementById('terms').checked;
    
    if (!firstName || !lastName || !email || !password || !confirmPassword) {
        alert('Please fill in all fields');
        return;
    }
    
    if (password !== confirmPassword) {
        alert('Passwords do not match');
        return;
    }
    
    if (!terms) {
        alert('Please accept the Terms of Service and Privacy Policy');
        return;
    }
    
    // Simulate successful registration
    alert('Account created successfully! Please check your email to verify your account.');
    showPage('login');
}

// Send reset email function
function sendResetEmail() {
    const email = document.getElementById('resetEmail').value;
    
    if (!email) {
        alert('Please enter your email address');
        return;
    }
    
    // Simulate sending reset email
    alert('Password reset link has been sent to your email address. Please check your inbox.');
    showPage('login');
}

// Reset password function
function performPasswordReset() {
    const newPassword = document.getElementById('newPassword').value;
    const confirmNewPassword = document.getElementById('confirmNewPassword').value;
    
    if (!newPassword || !confirmNewPassword) {
        alert('Please fill in both password fields');
        return;
    }
    
    if (newPassword !== confirmNewPassword) {
        alert('Passwords do not match');
        return;
    }
    
    if (newPassword.length < 8) {
        alert('Password must be at least 8 characters long');
        return;
    }
    
    // Simulate successful password reset
    alert('Password updated successfully! You can now sign in with your new password.');
    showPage('login');
}

// Profile page tabs
document.querySelectorAll('.profile-tab').forEach(tab => {
    tab.addEventListener('click', function() {
        // Update active tab
        document.querySelectorAll('.profile-tab').forEach(t => {
            t.classList.remove('active', 'border-blue-500', 'text-blue-600');
            t.classList.add('border-transparent', 'text-gray-500');
        });
        this.classList.add('active', 'border-blue-500', 'text-blue-600');
        this.classList.remove('border-transparent', 'text-gray-500');
        
        // Show corresponding content
        document.querySelectorAll('.profile-tab-content').forEach(content => {
            content.classList.add('hidden');
        });
        document.getElementById('profile-' + this.dataset.tab).classList.remove('hidden');
    });
});

// Metric Modal Functions
let currentMetricId = null;

function openMetricModal(metricId = null) {
    currentMetricId = metricId;
    const modal = document.getElementById('metricModal');
    const title = document.getElementById('metricModalTitle');
    
    if (metricId) {
        title.textContent = 'Edit Billable Metric';
        // Load existing data for editing
        loadMetricData(metricId);
    } else {
        title.textContent = 'Add Billable Metric';
        document.getElementById('metricForm').reset();
    }
    
    modal.classList.remove('hidden');
    modal.classList.add('flex');
}

function closeMetricModal() {
    const modal = document.getElementById('metricModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
    currentMetricId = null;
}

function loadMetricData(metricId) {
    // Mock data for editing
    const metricData = {
        'img_gen': {
            name: 'Image Generation',
            code: 'img_gen',
            type: 'per_unit',
            price: '0.05',
            description: 'Per image generated',
            status: 'active'
        },
        'vid_gen': {
            name: 'Video Generation',
            code: 'vid_gen',
            type: 'per_second',
            price: '0.25',
            description: 'Per second of video',
            status: 'active'
        },
        'api_call': {
            name: 'API Calls',
            code: 'api_call',
            type: 'per_unit',
            price: '0.01',
            description: 'Per API request',
            status: 'active'
        }
    };
    
    const data = metricData[metricId];
    if (data) {
        document.getElementById('metricName').value = data.name;
        document.getElementById('metricCode').value = data.code;
        document.getElementById('metricType').value = data.type;
        document.getElementById('metricPrice').value = data.price;
        document.getElementById('metricDescription').value = data.description;
        document.getElementById('metricStatus').value = data.status;
    }
}

function editMetric(metricId) {
    openMetricModal(metricId);
}

function deleteMetric(metricId) {
    if (confirm('Are you sure you want to delete this metric?')) {
        alert('Metric deleted successfully!');
        // In real app, would make API call to delete
    }
}

// User Modal Functions
let currentUserId = null;

function openUserModal(userId = null) {
    currentUserId = userId;
    const modal = document.getElementById('userModal');
    const title = document.getElementById('userModalTitle');
    
    if (userId) {
        title.textContent = 'Edit User';
        loadUserData(userId);
    } else {
        title.textContent = 'Add User';
        document.getElementById('userForm').reset();
    }
    
    modal.classList.remove('hidden');
    modal.classList.add('flex');
}

function closeUserModal() {
    const modal = document.getElementById('userModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
    currentUserId = null;
}

function loadUserData(userId) {
    // Mock data for editing
    const userData = {
        '<EMAIL>': {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            role: 'user',
            plan: 'pro',
            status: 'active',
            company: 'Tech Startup Inc.',
            notes: 'Active user with high engagement'
        },
        '<EMAIL>': {
            firstName: 'Alice',
            lastName: 'Smith',
            email: '<EMAIL>',
            role: 'admin',
            plan: 'enterprise',
            status: 'active',
            company: 'Enterprise Corp',
            notes: 'Admin user with full access'
        },
        '<EMAIL>': {
            firstName: 'Bob',
            lastName: 'Johnson',
            email: '<EMAIL>',
            role: 'user',
            plan: 'starter',
            status: 'pending',
            company: 'Startup Inc',
            notes: 'New user pending verification'
        }
    };
    
    const data = userData[userId];
    if (data) {
        document.getElementById('userFirstName').value = data.firstName;
        document.getElementById('userLastName').value = data.lastName;
        document.getElementById('userEmail').value = data.email;
        document.getElementById('userRole').value = data.role;
        document.getElementById('userPlan').value = data.plan;
        document.getElementById('userStatus').value = data.status;
        document.getElementById('userCompany').value = data.company;
        document.getElementById('userNotes').value = data.notes;
    }
}

function editUser(userId) {
    openUserModal(userId);
}

function deactivateUser(userId) {
    if (confirm('Are you sure you want to deactivate this user?')) {
        alert('User deactivated successfully!');
        // In real app, would make API call to deactivate
    }
}

// Role Modal Functions
let currentRoleId = null;

function openRoleModal(roleId = null) {
    currentRoleId = roleId;
    const modal = document.getElementById('roleModal');
    const title = document.getElementById('roleModalTitle');
    
    if (roleId) {
        title.textContent = 'Edit Role';
        loadRoleData(roleId);
    } else {
        title.textContent = 'Add Role';
        document.getElementById('roleForm').reset();
        resetRolePermissions();
    }
    
    modal.classList.remove('hidden');
    modal.classList.add('flex');
}

function closeRoleModal() {
    const modal = document.getElementById('roleModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
    currentRoleId = null;
}

function loadRoleData(roleId) {
    // Mock data for editing
    const roleData = {
        'super_admin': {
            name: 'Super Admin',
            code: 'super_admin',
            description: 'Full system access and control',
            permissions: {
                'perm_user_view': true,
                'perm_user_create': true,
                'perm_user_edit': true,
                'perm_user_delete': true,
                'perm_ai_generate': true,
                'perm_ai_advanced': true,
                'perm_ai_api': true,
                'perm_billing_view': true,
                'perm_billing_manage': true,
                'perm_billing_refund': true,
                'perm_system_view': true,
                'perm_system_edit': true,
                'perm_system_logs': true
            }
        },
        'admin': {
            name: 'Admin',
            code: 'admin',
            description: 'Manage users and content',
            permissions: {
                'perm_user_view': true,
                'perm_user_create': true,
                'perm_user_edit': true,
                'perm_user_delete': false,
                'perm_ai_generate': true,
                'perm_ai_advanced': true,
                'perm_ai_api': true,
                'perm_billing_view': false,
                'perm_billing_manage': false,
                'perm_billing_refund': false,
                'perm_system_view': false,
                'perm_system_edit': false,
                'perm_system_logs': false
            }
        },
        'user': {
            name: 'User',
            code: 'user',
            description: 'Standard user access',
            permissions: {
                'perm_user_view': false,
                'perm_user_create': false,
                'perm_user_edit': false,
                'perm_user_delete': false,
                'perm_ai_generate': true,
                'perm_ai_advanced': false,
                'perm_ai_api': false,
                'perm_billing_view': false,
                'perm_billing_manage': false,
                'perm_billing_refund': false,
                'perm_system_view': false,
                'perm_system_edit': false,
                'perm_system_logs': false
            }
        },
        'enterprise': {
            name: 'Enterprise',
            code: 'enterprise',
            description: 'Enterprise-level access with advanced features',
            permissions: {
                'perm_user_view': false,
                'perm_user_create': false,
                'perm_user_edit': false,
                'perm_user_delete': false,
                'perm_ai_generate': true,
                'perm_ai_advanced': true,
                'perm_ai_api': true,
                'perm_billing_view': true,
                'perm_billing_manage': false,
                'perm_billing_refund': false,
                'perm_system_view': false,
                'perm_system_edit': false,
                'perm_system_logs': false
            }
        }
    };
    
    const data = roleData[roleId];
    if (data) {
        document.getElementById('roleName').value = data.name;
        document.getElementById('roleCode').value = data.code;
        document.getElementById('roleDescription').value = data.description;
        
        // Set permissions
        Object.keys(data.permissions).forEach(permId => {
            const checkbox = document.getElementById(permId);
            if (checkbox) {
                checkbox.checked = data.permissions[permId];
            }
        });
    }
}

function editRole(roleId) {
    openRoleModal(roleId);
}

function deleteRole(roleId) {
    if (confirm('Are you sure you want to delete this role?')) {
        alert('Role deleted successfully!');
        // In real app, would make API call to delete
    }
}

function resetRolePermissions() {
    const checkboxes = document.querySelectorAll('#roleForm input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

// Plan Modal Functions
let currentPlanId = null;

function openPlanModal(planId = null) {
    currentPlanId = planId;
    const modal = document.getElementById('planModal');
    const title = document.getElementById('planModalTitle');
    
    if (planId) {
        title.textContent = 'Edit Subscription Plan';
        loadPlanData(planId);
    } else {
        title.textContent = 'Add Subscription Plan';
        document.getElementById('planForm').reset();
        resetFeatureFields();
    }
    
    modal.classList.remove('hidden');
    modal.classList.add('flex');
}

function closePlanModal() {
    const modal = document.getElementById('planModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
    currentPlanId = null;
}

function loadPlanData(planId) {
    // Mock data for editing
    const planData = {
        'starter': {
            name: 'Starter',
            code: 'starter',
            price: '9',
            billingCycle: 'monthly',
            status: 'active',
            description: 'Perfect for individuals getting started',
            features: ['100 AI generations/month', 'Basic image models', 'Standard support'],
            imageLimit: '100',
            videoLimit: '10',
            apiLimit: '1000'
        },
        'pro': {
            name: 'Pro',
            code: 'pro',
            price: '29',
            billingCycle: 'monthly',
            status: 'active',
            description: 'Best for growing businesses',
            features: ['1,000 AI generations/month', 'All AI models available', 'Priority support', 'API access'],
            imageLimit: '1000',
            videoLimit: '100',
            apiLimit: '10000'
        },
        'enterprise': {
            name: 'Enterprise',
            code: 'enterprise',
            price: '99',
            billingCycle: 'monthly',
            status: 'active',
            description: 'For large-scale operations',
            features: ['Unlimited generations', 'Custom model training', '24/7 dedicated support', 'On-premise deployment'],
            imageLimit: '999999',
            videoLimit: '999999',
            apiLimit: '999999'
        }
    };
    
    const data = planData[planId];
    if (data) {
        document.getElementById('planName').value = data.name;
        document.getElementById('planCode').value = data.code;
        document.getElementById('planPrice').value = data.price;
        document.getElementById('planBillingCycle').value = data.billingCycle;
        document.getElementById('planStatus').value = data.status;
        document.getElementById('planDescription').value = data.description;
        document.getElementById('planImageLimit').value = data.imageLimit;
        document.getElementById('planVideoLimit').value = data.videoLimit;
        document.getElementById('planApiLimit').value = data.apiLimit;
        
        // Load features
        resetFeatureFields();
        data.features.forEach((feature, index) => {
            if (index === 0) {
                document.querySelector('#planFeatures input').value = feature;
            } else {
                addFeatureField(feature);
            }
        });
    }
}

function editPlan(planId) {
    openPlanModal(planId);
}

function deletePlan(planId) {
    if (confirm('Are you sure you want to delete this plan?')) {
        alert('Plan deleted successfully!');
        // In real app, would make API call to delete
    }
}

function addFeatureField(value = '') {
    const featuresContainer = document.getElementById('planFeatures');
    const newField = document.createElement('div');
    newField.className = 'flex items-center space-x-2';
    newField.innerHTML = `
        <input type="text" class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., 1000 AI generations/month" value="${value}">
        <button type="button" onclick="addFeatureField()" class="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700">
            <i class="fas fa-plus"></i>
        </button>
        <button type="button" onclick="removeFeatureField(this)" class="bg-red-600 text-white px-3 py-2 rounded-lg hover:bg-red-700">
            <i class="fas fa-minus"></i>
        </button>
    `;
    featuresContainer.appendChild(newField);
}

function removeFeatureField(button) {
    const featuresContainer = document.getElementById('planFeatures');
    if (featuresContainer.children.length > 1) {
        button.parentElement.remove();
    }
}

function resetFeatureFields() {
    const featuresContainer = document.getElementById('planFeatures');
    featuresContainer.innerHTML = `
        <div class="flex items-center space-x-2">
            <input type="text" class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., 1000 AI generations/month">
            <button type="button" onclick="addFeatureField()" class="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700">
                <i class="fas fa-plus"></i>
            </button>
            <button type="button" onclick="removeFeatureField(this)" class="bg-red-600 text-white px-3 py-2 rounded-lg hover:bg-red-700">
                <i class="fas fa-minus"></i>
            </button>
        </div>
    `;
}

// Form submission handlers
document.getElementById('userForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        firstName: document.getElementById('userFirstName').value,
        lastName: document.getElementById('userLastName').value,
        email: document.getElementById('userEmail').value,
        role: document.getElementById('userRole').value,
        plan: document.getElementById('userPlan').value,
        status: document.getElementById('userStatus').value,
        company: document.getElementById('userCompany').value,
        notes: document.getElementById('userNotes').value
    };
    
    if (currentUserId) {
        alert('User updated successfully!');
    } else {
        alert('User created successfully!');
    }
    
    closeUserModal();
});

document.getElementById('roleForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const permissions = {};
    const checkboxes = document.querySelectorAll('#roleForm input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        permissions[checkbox.id] = checkbox.checked;
    });
    
    const formData = {
        name: document.getElementById('roleName').value,
        code: document.getElementById('roleCode').value,
        description: document.getElementById('roleDescription').value,
        permissions: permissions
    };
    
    if (currentRoleId) {
        alert('Role updated successfully!');
    } else {
        alert('Role created successfully!');
    }
    
    closeRoleModal();
});

document.getElementById('metricForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        name: document.getElementById('metricName').value,
        code: document.getElementById('metricCode').value,
        type: document.getElementById('metricType').value,
        price: document.getElementById('metricPrice').value,
        description: document.getElementById('metricDescription').value,
        status: document.getElementById('metricStatus').value
    };
    
    if (currentMetricId) {
        alert('Metric updated successfully!');
    } else {
        alert('Metric created successfully!');
    }
    
    closeMetricModal();
});

document.getElementById('planForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const features = Array.from(document.querySelectorAll('#planFeatures input')).map(input => input.value).filter(value => value.trim() !== '');
    
    const formData = {
        name: document.getElementById('planName').value,
        code: document.getElementById('planCode').value,
        price: document.getElementById('planPrice').value,
        billingCycle: document.getElementById('planBillingCycle').value,
        status: document.getElementById('planStatus').value,
        description: document.getElementById('planDescription').value,
        features: features,
        imageLimit: document.getElementById('planImageLimit').value,
        videoLimit: document.getElementById('planVideoLimit').value,
        apiLimit: document.getElementById('planApiLimit').value
    };
    
    if (currentPlanId) {
        alert('Plan updated successfully!');
    } else {
        alert('Plan created successfully!');
    }
    
    closePlanModal();
});

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    showPage('home');
});
</script>

</body>
</html>