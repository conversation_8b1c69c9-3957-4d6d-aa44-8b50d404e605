# API 快速参考

## 基础配置

```typescript
// API 基础 URL
const API_BASE_URL = 'http://localhost:3000/api'

// 请求头配置
const headers = {
  'Content-Type': 'application/json',
  'Authorization': 'Bearer YOUR_TOKEN'
}
```

## 常用接口列表

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | `/auth/login` | 用户登录 | ❌ |
| POST | `/auth/register` | 用户注册 | ❌ |
| GET | `/auth/me` | 获取用户信息 | ✅ |
| PUT | `/auth/profile` | 更新用户信息 | ✅ |
| GET | `/videos` | 获取视频列表 | ✅ |
| GET | `/videos/:id` | 获取视频详情 | ✅ |
| POST | `/videos` | 上传视频 | ✅ |
| PUT | `/videos/:id` | 更新视频 | ✅ |
| DELETE | `/videos/:id` | 删除视频 | ✅ |
| GET | `/playlists` | 获取播放列表 | ✅ |
| POST | `/playlists` | 创建播放列表 | ✅ |
| GET | `/analytics/stats` | 获取统计数据 | ✅ |

## 常用请求示例

### 登录请求

```typescript
const loginData = {
  username: 'testuser',
  password: 'password123'
}

const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(loginData)
})
```

### 带认证的请求

```typescript
const response = await fetch('/api/videos', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN'
  }
})
```

### 分页请求

```typescript
const params = new URLSearchParams({
  page: '1',
  limit: '10',
  search: 'keyword'
})

const response = await fetch(`/api/videos?${params}`, {
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN'
  }
})
```

### 文件上传

```typescript
const formData = new FormData()
formData.append('file', file)
formData.append('title', '视频标题')
formData.append('description', '视频描述')

const response = await fetch('/api/videos', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: formData
})
```

## 响应格式

### 成功响应

```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

### 错误响应

```json
{
  "success": false,
  "error": "错误信息",
  "code": 400
}
```

### 分页响应

```json
{
  "data": [],
  "total": 100,
  "page": 1,
  "limit": 10,
  "pages": 10
}
```

## 状态码

- `200` - 成功
- `201` - 创建成功
- `400` - 请求错误
- `401` - 未授权
- `403` - 禁止访问
- `404` - 资源不存在
- `500` - 服务器错误

## 前端集成

### Axios 配置

```typescript
import axios from 'axios'

const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  timeout: 10000
})

// 请求拦截器
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 响应拦截器
api.interceptors.response.use(
  response => response.data,
  error => {
    if (error.response?.status === 401) {
      // 处理未授权
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)
```

### 使用示例

```typescript
// 登录
const login = async (credentials) => {
  const response = await api.post('/auth/login', credentials)
  localStorage.setItem('token', response.token)
  return response
}

// 获取视频列表
const getVideos = async (params) => {
  return await api.get('/videos', { params })
}

// 上传视频
const uploadVideo = async (formData) => {
  return await api.post('/videos', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
``` 