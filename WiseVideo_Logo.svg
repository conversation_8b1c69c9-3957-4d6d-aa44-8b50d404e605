<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="60" viewBox="0 0 200 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle for icon -->
  <circle cx="30" cy="30" r="25" fill="url(#gradient1)" stroke="url(#gradient2)" stroke-width="2"/>
  
  <!-- Play button symbol (video element) -->
  <path d="M22 20L22 40L38 30L22 20Z" fill="white" opacity="0.9"/>
  
  <!-- AI circuit pattern overlay -->
  <g opacity="0.3">
    <circle cx="25" cy="25" r="2" fill="white"/>
    <circle cx="35" cy="25" r="1.5" fill="white"/>
    <circle cx="30" cy="35" r="1.5" fill="white"/>
    <line x1="25" y1="25" x2="33" y2="25" stroke="white" stroke-width="1"/>
    <line x1="30" y1="27" x2="30" y2="33" stroke="white" stroke-width="1"/>
  </g>
  
  <!-- Brand text -->
  <text x="65" y="25" font-family="Inter, system-ui, -apple-system, sans-serif" font-size="18" font-weight="700" fill="#1a1a1a">
    Wise
  </text>
  <text x="65" y="42" font-family="Inter, system-ui, -apple-system, sans-serif" font-size="18" font-weight="500" fill="#6366f1">
    Video
  </text>
  
  <!-- Subtle AI accent dots -->
  <circle cx="125" cy="20" r="2" fill="#6366f1" opacity="0.6"/>
  <circle cx="135" cy="25" r="1.5" fill="#8b5cf6" opacity="0.5"/>
  <circle cx="145" cy="18" r="1" fill="#06b6d4" opacity="0.7"/>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg> 