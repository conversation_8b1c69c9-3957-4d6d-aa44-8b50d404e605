# 前端项目设置指南

## 项目概述

这是一个基于 Vue 3 + TypeScript + Element Plus + Tailwind CSS 的现代化视频平台前端项目。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript 超集
- **Element Plus** - Vue 3 组件库
- **Tailwind CSS** - 实用优先的 CSS 框架
- **Vite** - 下一代前端构建工具
- **Vue Router** - Vue.js 官方路由管理器
- **Pinia** - Vue 状态管理库
- **Axios** - HTTP 客户端

## 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0

## 项目创建步骤

### 1. 创建 Vue 项目

```bash
npm create vue@latest wise-video-fe
cd wise-video-fe
```

选择以下选项：
- ✅ TypeScript
- ✅ JSX
- ✅ Vue Router
- ✅ Pinia
- ✅ Vitest
- ✅ End-to-End Testing
- ✅ ESLint
- ✅ Prettier

### 2. 安装依赖

```bash
npm install
```

### 3. 安装额外依赖

```bash
# Element Plus
npm install element-plus @element-plus/icons-vue

# Tailwind CSS
npm install -D tailwindcss postcss autoprefixer

# HTTP 客户端
npm install axios
```

### 4. 配置 Tailwind CSS

```bash
npx tailwindcss init -p
```

更新 `tailwind.config.js`:

```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        }
      }
    },
  },
  plugins: [],
}
```

### 5. 配置 Element Plus

在 `src/main.ts` 中添加：

```typescript
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(ElementPlus)
```

### 6. 配置 CSS

在 `src/assets/main.css` 中添加：

```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

## 项目结构

```
src/
├── assets/          # 静态资源
│   ├── main.css     # 主样式文件
│   └── logo.svg     # Logo
├── components/      # 公共组件
├── router/          # 路由配置
│   └── index.ts     # 路由定义
├── stores/          # 状态管理
│   └── user.ts      # 用户状态
├── utils/           # 工具函数
│   └── api.ts       # API 配置
├── views/           # 页面组件
│   ├── LoginView.vue
│   ├── RegisterView.vue
│   ├── DashboardView.vue
│   └── ...
├── App.vue          # 根组件
└── main.ts          # 入口文件
```

## API 集成配置

### 1. 创建 API 配置文件

创建 `src/utils/api.ts`:

```typescript
import axios from 'axios'
import { ElMessage } from 'element-plus'

const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    const { response } = error
    
    if (response) {
      switch (response.status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          localStorage.removeItem('token')
          break
        case 403:
          ElMessage.error('拒绝访问')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(response.data?.message || '请求失败')
      }
    } else {
      ElMessage.error('网络错误，请检查网络连接')
    }
    
    return Promise.reject(error)
  }
)

export default api
```

### 2. 环境变量配置

创建 `.env.local` 文件：

```env
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_TITLE=Wise Video
```

## 状态管理设置

### 1. 用户状态管理

创建 `src/stores/user.ts`:

```typescript
import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/utils/api'

export interface User {
  id: number
  username: string
  email: string
  avatar?: string
  role: string
}

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const isAuthenticated = ref<boolean>(!!token.value)

  const login = async (credentials: { username: string; password: string }) => {
    try {
      const response: any = await api.post('/auth/login', credentials)
      const { token: newToken, user: userData } = response
      
      token.value = newToken
      user.value = userData
      isAuthenticated.value = true
      
      localStorage.setItem('token', newToken)
      
      return { success: true }
    } catch (error) {
      return { success: false, error }
    }
  }

  const logout = () => {
    user.value = null
    token.value = null
    isAuthenticated.value = false
    localStorage.removeItem('token')
  }

  return {
    user,
    token,
    isAuthenticated,
    login,
    logout,
  }
})
```

## 路由设置

### 1. 路由配置

更新 `src/router/index.ts`:

```typescript
import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    }
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth && !userStore.isAuthenticated) {
    next('/login')
    return
  }
  
  if (to.meta.requiresGuest && userStore.isAuthenticated) {
    next('/dashboard')
    return
  }
  
  next()
})

export default router
```

## 开发指南

### 1. 启动开发服务器

```bash
npm run dev
```

### 2. 构建生产版本

```bash
npm run build
```

### 3. 代码检查

```bash
npm run lint
```

### 4. 类型检查

```bash
npm run type-check
```

## 最佳实践

### 1. 组件开发

- 使用 Composition API
- 使用 TypeScript 类型定义
- 遵循单一职责原则
- 使用 Element Plus 组件

### 2. 样式开发

- 优先使用 Tailwind CSS 类
- 遵循响应式设计原则
- 保持设计一致性

### 3. 状态管理

- 使用 Pinia 进行状态管理
- 按功能模块划分 store
- 避免过度使用全局状态

### 4. API 调用

- 使用统一的 API 配置
- 处理错误和加载状态
- 使用 TypeScript 类型定义

## 常见问题

### 1. 图标不显示

确保已正确导入 Element Plus 图标：

```typescript
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
```

### 2. Tailwind CSS 样式不生效

检查 `tailwind.config.js` 中的 content 配置是否正确包含了所有 Vue 文件。

### 3. 路由跳转问题

确保路由守卫配置正确，检查 `meta` 字段设置。

### 4. API 请求失败

检查环境变量配置和 API 基础 URL 设置。

## 部署

### 1. 构建

```bash
npm run build
```

### 2. 预览

```bash
npm run preview
```

### 3. 部署到服务器

将 `dist` 目录中的文件部署到 Web 服务器即可。 