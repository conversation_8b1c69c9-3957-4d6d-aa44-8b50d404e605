# Wise Video Frontend

基于 Vue 3 + TypeScript + Element Plus + Tailwind CSS 的现代化视频平台前端项目。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript 超集
- **Element Plus** - Vue 3 组件库
- **Tailwind CSS** - 实用优先的 CSS 框架
- **Vite** - 下一代前端构建工具
- **Vue Router** - Vue.js 官方路由管理器
- **Pinia** - Vue 状态管理库
- **Axios** - HTTP 客户端

## 项目结构

```
src/
├── assets/          # 静态资源
├── components/      # 公共组件
├── router/          # 路由配置
├── stores/          # 状态管理
├── utils/           # 工具函数
├── views/           # 页面组件
├── App.vue          # 根组件
└── main.ts          # 入口文件
```

## 功能特性

- 🔐 用户认证（登录/注册）
- 📊 数据仪表板
- 🎥 视频管理
- 📈 数据分析
- 📋 播放列表
- ⚙️ 用户设置
- 📱 响应式设计

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发环境

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
```

### 类型检查

```bash
npm run type-check
```

## 环境变量

创建 `.env.local` 文件并配置以下变量：

```env
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_TITLE=Wise Video
```

## 路由说明

- `/` - 重定向到仪表板
- `/login` - 用户登录
- `/register` - 用户注册
- `/dashboard` - 主仪表板
- `/upload` - 视频上传
- `/videos` - 视频列表
- `/videos/:id` - 视频详情
- `/profile` - 个人资料
- `/settings` - 用户设置
- `/analytics` - 数据分析
- `/playlists` - 播放列表
- `/playlists/create` - 创建播放列表

## API 集成

项目已配置 Axios 实例，支持：

- 自动添加认证 token
- 请求/响应拦截器
- 错误处理
- 统一的消息提示

## 开发指南

### 添加新页面

1. 在 `src/views/` 目录下创建新的 Vue 组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 根据需要添加路由守卫

### 状态管理

使用 Pinia 进行状态管理，主要 store：

- `user` - 用户信息和认证状态

### 样式指南

- 使用 Tailwind CSS 进行样式开发
- 遵循 Element Plus 设计规范
- 响应式设计优先

## 部署

### 构建

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
