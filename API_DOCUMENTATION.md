# API 文档

## 基础信息

- **基础 URL**: `http://localhost:3000/api`
- **认证方式**: <PERSON><PERSON>
- **内容类型**: `application/json`

## 认证接口

### 用户登录

**POST** `/auth/login`

**请求体**:
```json
{
  "username": "string",
  "password": "string"
}
```

**响应**:
```json
{
  "success": true,
  "token": "string",
  "user": {
    "id": 1,
    "username": "string",
    "email": "string",
    "avatar": "string",
    "role": "string"
  }
}
```

### 用户注册

**POST** `/auth/register`

**请求体**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string"
}
```

**响应**:
```json
{
  "success": true,
  "message": "注册成功"
}
```

### 获取用户信息

**GET** `/auth/me`

**响应**:
```json
{
  "id": 1,
  "username": "string",
  "email": "string",
  "avatar": "string",
  "role": "string"
}
```

### 更新用户信息

**PUT** `/auth/profile`

**请求体**:
```json
{
  "username": "string",
  "email": "string",
  "avatar": "string"
}
```

## 视频接口

### 获取视频列表

**GET** `/videos`

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `search`: 搜索关键词
- `category`: 分类

**响应**:
```json
{
  "videos": [
    {
      "id": 1,
      "title": "string",
      "description": "string",
      "thumbnail": "string",
      "videoUrl": "string",
      "duration": 120,
      "views": 1000,
      "likes": 50,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "limit": 10
}
```

### 获取视频详情

**GET** `/videos/:id`

**响应**:
```json
{
  "id": 1,
  "title": "string",
  "description": "string",
  "thumbnail": "string",
  "videoUrl": "string",
  "duration": 120,
  "views": 1000,
  "likes": 50,
  "createdAt": "2024-01-01T00:00:00Z",
  "author": {
    "id": 1,
    "username": "string",
    "avatar": "string"
  }
}
```

### 上传视频

**POST** `/videos`

**请求体** (multipart/form-data):
```
file: 视频文件
title: 标题
description: 描述
category: 分类
```

### 更新视频

**PUT** `/videos/:id`

**请求体**:
```json
{
  "title": "string",
  "description": "string",
  "category": "string"
}
```

### 删除视频

**DELETE** `/videos/:id`

## 播放列表接口

### 获取播放列表

**GET** `/playlists`

**响应**:
```json
{
  "playlists": [
    {
      "id": 1,
      "name": "string",
      "description": "string",
      "thumbnail": "string",
      "videoCount": 10,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 创建播放列表

**POST** `/playlists`

**请求体**:
```json
{
  "name": "string",
  "description": "string"
}
```

## 数据分析接口

### 获取统计数据

**GET** `/analytics/stats`

**响应**:
```json
{
  "totalVideos": 100,
  "totalViews": 50000,
  "totalLikes": 2500,
  "followers": 500,
  "monthlyViews": [
    {
      "month": "2024-01",
      "views": 1000
    }
  ]
}
```

## 错误码

| 状态码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 前端集成示例

```typescript
import api from '@/utils/api'

// 登录
const login = async (credentials: { username: string; password: string }) => {
  try {
    const response = await api.post('/auth/login', credentials)
    return response
  } catch (error) {
    console.error('Login failed:', error)
    throw error
  }
}

// 获取视频列表
const getVideos = async (params: { page?: number; limit?: number }) => {
  try {
    const response = await api.get('/videos', { params })
    return response
  } catch (error) {
    console.error('Failed to fetch videos:', error)
    throw error
  }
}
``` 