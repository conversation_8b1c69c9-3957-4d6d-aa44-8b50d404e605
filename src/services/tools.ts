import api from '@/utils/api'
import type { AITool, ToolCategory } from '@/types'

export const toolsService = {
  // 获取所有工具
  async getTools(): Promise<AITool[]> {
    return await api.get('/tools')
  },

  // 根据分类获取工具
  async getToolsByCategory(category: ToolCategory): Promise<AITool[]> {
    return await api.get('/tools', { params: { category } })
  },

  // 获取工具详情
  async getToolById(toolId: string): Promise<AITool> {
    return await api.get(`/tools/${toolId}`)
  },

  // 获取工具配置
  async getToolConfig(toolId: string): Promise<any> {
    return await api.get(`/tools/${toolId}/config`)
  }
}
