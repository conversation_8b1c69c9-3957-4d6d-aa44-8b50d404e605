import api from '@/utils/api'
import type { 
  AdminStats, 
  AdminUser, 
  AITool,
  PaginationParams,
  PaginationResponse
} from '@/types'

export const adminService = {
  // 获取管理员统计
  async getStats(): Promise<AdminStats> {
    return await api.get('/admin/stats')
  },

  // 获取用户列表
  async getUsers(params?: PaginationParams & { 
    search?: string 
    role?: string 
    status?: string 
  }): Promise<PaginationResponse & { users: AdminUser[] }> {
    return await api.get('/admin/users', { params })
  },

  // 获取用户详情
  async getUserById(userId: number): Promise<AdminUser> {
    return await api.get(`/admin/users/${userId}`)
  },

  // 更新用户状态
  async updateUserStatus(userId: number, status: 'active' | 'suspended' | 'banned'): Promise<void> {
    return await api.put(`/admin/users/${userId}/status`, { status })
  },

  // 更新用户角色
  async updateUserRole(userId: number, role: string): Promise<void> {
    return await api.put(`/admin/users/${userId}/role`, { role })
  },

  // 获取工具管理列表
  async getToolsManagement(): Promise<AITool[]> {
    return await api.get('/admin/tools')
  },

  // 更新工具配置
  async updateToolConfig(toolId: string, config: any): Promise<AITool> {
    return await api.put(`/admin/tools/${toolId}`, config)
  },

  // 启用/禁用工具
  async toggleTool(toolId: string, enabled: boolean): Promise<void> {
    return await api.put(`/admin/tools/${toolId}/toggle`, { enabled })
  },

  // 获取系统日志
  async getSystemLogs(params?: {
    level?: 'info' | 'warn' | 'error'
    startDate?: string
    endDate?: string
    page?: number
    limit?: number
  }): Promise<any> {
    return await api.get('/admin/logs', { params })
  }
}
