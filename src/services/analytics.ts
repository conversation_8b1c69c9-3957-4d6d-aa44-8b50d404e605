import api from '@/utils/api'
import type { AnalyticsStats } from '@/types'

export const analyticsService = {
  // 获取统计数据
  async getStats(): Promise<AnalyticsStats> {
    return await api.get('/analytics/stats')
  },

  // 获取视频观看统计
  async getVideoStats(videoId: number): Promise<any> {
    return await api.get(`/analytics/videos/${videoId}`)
  },

  // 获取用户统计
  async getUserStats(): Promise<any> {
    return await api.get('/analytics/user')
  },

  // 获取时间范围内的统计
  async getStatsInRange(startDate: string, endDate: string): Promise<any> {
    return await api.get('/analytics/stats', {
      params: {
        startDate,
        endDate
      }
    })
  },

  // 获取热门视频
  async getTrendingVideos(limit: number = 10): Promise<any> {
    return await api.get('/analytics/trending', {
      params: { limit }
    })
  },

  // 获取观看时长统计
  async getWatchTimeStats(): Promise<any> {
    return await api.get('/analytics/watch-time')
  }
}
