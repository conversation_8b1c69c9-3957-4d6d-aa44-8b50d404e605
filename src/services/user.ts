import api from '@/utils/api'
import type { 
  User, 
  UserSettings, 
  BillingInfo, 
  UsageStats,
  Notification
} from '@/types'

export const userService = {
  // 获取用户资料
  async getProfile(): Promise<User> {
    return await api.get('/user/profile')
  },

  // 更新用户资料
  async updateProfile(data: Partial<User>): Promise<User> {
    return await api.put('/user/profile', data)
  },

  // 上传头像
  async uploadAvatar(file: File): Promise<{ avatarUrl: string }> {
    const formData = new FormData()
    formData.append('avatar', file)
    
    return await api.post('/user/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取用户设置
  async getSettings(): Promise<UserSettings> {
    return await api.get('/user/settings')
  },

  // 更新用户设置
  async updateSettings(settings: Partial<UserSettings>): Promise<UserSettings> {
    return await api.put('/user/settings', settings)
  },

  // 获取账单信息
  async getBillingInfo(): Promise<BillingInfo> {
    return await api.get('/user/billing')
  },

  // 获取使用统计
  async getUsageStats(): Promise<UsageStats> {
    return await api.get('/user/usage')
  },

  // 获取通知列表
  async getNotifications(): Promise<Notification[]> {
    return await api.get('/user/notifications')
  },

  // 标记通知为已读
  async markNotificationAsRead(notificationId: string): Promise<void> {
    return await api.put(`/user/notifications/${notificationId}/read`)
  },

  // 标记所有通知为已读
  async markAllNotificationsAsRead(): Promise<void> {
    return await api.put('/user/notifications/read-all')
  },

  // 删除通知
  async deleteNotification(notificationId: string): Promise<void> {
    return await api.delete(`/user/notifications/${notificationId}`)
  }
}
