import api from '@/utils/api'
import type { 
  Video, 
  VideoListResponse, 
  VideoListParams,
  UploadVideoRequest,
  UpdateVideoRequest 
} from '@/types'

export const videoService = {
  // 获取视频列表
  async getVideos(params?: VideoListParams): Promise<VideoListResponse> {
    return await api.get('/videos', { params })
  },

  // 获取视频详情
  async getVideoById(id: number): Promise<Video> {
    return await api.get(`/videos/${id}`)
  },

  // 上传视频
  async uploadVideo(data: UploadVideoRequest): Promise<Video> {
    const formData = new FormData()
    formData.append('file', data.file)
    formData.append('title', data.title)
    formData.append('description', data.description)
    formData.append('category', data.category)

    return await api.post('/videos', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 更新视频信息
  async updateVideo(id: number, data: UpdateVideoRequest): Promise<Video> {
    return await api.put(`/videos/${id}`, data)
  },

  // 删除视频
  async deleteVideo(id: number): Promise<void> {
    return await api.delete(`/videos/${id}`)
  },

  // 搜索视频
  async searchVideos(query: string, params?: VideoListParams): Promise<VideoListResponse> {
    return await api.get('/videos', { 
      params: { 
        ...params, 
        search: query 
      } 
    })
  },

  // 按分类获取视频
  async getVideosByCategory(category: string, params?: VideoListParams): Promise<VideoListResponse> {
    return await api.get('/videos', { 
      params: { 
        ...params, 
        category 
      } 
    })
  },

  // 点赞视频
  async likeVideo(id: number): Promise<void> {
    return await api.post(`/videos/${id}/like`)
  },

  // 取消点赞
  async unlikeVideo(id: number): Promise<void> {
    return await api.delete(`/videos/${id}/like`)
  },

  // 增加播放次数
  async incrementViews(id: number): Promise<void> {
    return await api.post(`/videos/${id}/view`)
  }
}
