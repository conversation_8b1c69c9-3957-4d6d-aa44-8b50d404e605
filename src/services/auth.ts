import api from '@/utils/api'
import type {
  LoginRequest,
  RegisterRequest,
  LoginResponse,
  RegisterResponse,
  User,
  UpdateProfileRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest
} from '@/types'

export const authService = {
  // 用户登录
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    return await api.post('/auth/login', credentials)
  },

  // 用户注册
  async register(userData: RegisterRequest): Promise<RegisterResponse> {
    return await api.post('/auth/register', userData)
  },

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    return await api.get('/auth/me')
  },

  // 更新用户资料
  async updateProfile(profileData: UpdateProfileRequest): Promise<User> {
    return await api.put('/auth/profile', profileData)
  },

  // 忘记密码
  async forgotPassword(data: ForgotPasswordRequest): Promise<{ success: boolean; message: string }> {
    return await api.post('/auth/forgot-password', data)
  },

  // 重置密码
  async resetPassword(data: ResetPasswordRequest): Promise<{ success: boolean; message: string }> {
    return await api.post('/auth/reset-password', data)
  },

  // 退出登录
  async logout(): Promise<void> {
    return await api.post('/auth/logout')
  },

  // 第三方登录
  async socialLogin(provider: 'google' | 'github', token: string): Promise<LoginResponse> {
    return await api.post(`/auth/social/${provider}`, { token })
  }
}
