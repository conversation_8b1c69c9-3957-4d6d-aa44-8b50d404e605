import api from '@/utils/api'
import type { 
  LoginRequest, 
  RegisterRequest, 
  LoginResponse, 
  RegisterResponse, 
  User,
  UpdateProfileRequest 
} from '@/types'

export const authService = {
  // 用户登录
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    return await api.post('/auth/login', credentials)
  },

  // 用户注册
  async register(userData: RegisterRequest): Promise<RegisterResponse> {
    return await api.post('/auth/register', userData)
  },

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    return await api.get('/auth/me')
  },

  // 更新用户资料
  async updateProfile(profileData: UpdateProfileRequest): Promise<User> {
    return await api.put('/auth/profile', profileData)
  },

  // 退出登录
  async logout(): Promise<void> {
    // 如果后端有退出登录接口，可以在这里调用
    // return await api.post('/auth/logout')
  }
}
