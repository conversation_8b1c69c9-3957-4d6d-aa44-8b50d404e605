import api from '@/utils/api'
import type { 
  Playlist, 
  PlaylistListResponse, 
  CreatePlaylistRequest 
} from '@/types'

export const playlistService = {
  // 获取播放列表
  async getPlaylists(): Promise<PlaylistListResponse> {
    return await api.get('/playlists')
  },

  // 获取播放列表详情
  async getPlaylistById(id: number): Promise<Playlist> {
    return await api.get(`/playlists/${id}`)
  },

  // 创建播放列表
  async createPlaylist(data: CreatePlaylistRequest): Promise<Playlist> {
    return await api.post('/playlists', data)
  },

  // 更新播放列表
  async updatePlaylist(id: number, data: Partial<CreatePlaylistRequest>): Promise<Playlist> {
    return await api.put(`/playlists/${id}`, data)
  },

  // 删除播放列表
  async deletePlaylist(id: number): Promise<void> {
    return await api.delete(`/playlists/${id}`)
  },

  // 添加视频到播放列表
  async addVideoToPlaylist(playlistId: number, videoId: number): Promise<void> {
    return await api.post(`/playlists/${playlistId}/videos`, { videoId })
  },

  // 从播放列表移除视频
  async removeVideoFromPlaylist(playlistId: number, videoId: number): Promise<void> {
    return await api.delete(`/playlists/${playlistId}/videos/${videoId}`)
  },

  // 获取播放列表中的视频
  async getPlaylistVideos(playlistId: number): Promise<any> {
    return await api.get(`/playlists/${playlistId}/videos`)
  }
}
