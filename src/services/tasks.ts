import api from '@/utils/api'
import type { 
  Task, 
  TaskListResponse, 
  TaskListParams, 
  CreateTaskRequest,
  TaskStatus,
  ToolCategory
} from '@/types'

export const tasksService = {
  // 获取任务列表
  async getTasks(params?: TaskListParams): Promise<TaskListResponse> {
    return await api.get('/tasks', { params })
  },

  // 获取任务详情
  async getTaskById(taskId: string): Promise<Task> {
    return await api.get(`/tasks/${taskId}`)
  },

  // 创建新任务
  async createTask(data: CreateTaskRequest): Promise<Task> {
    const formData = new FormData()
    
    formData.append('toolId', data.toolId)
    if (data.prompt) {
      formData.append('prompt', data.prompt)
    }
    formData.append('parameters', JSON.stringify(data.parameters))
    
    if (data.files) {
      data.files.forEach((file, index) => {
        formData.append(`files[${index}]`, file)
      })
    }

    return await api.post('/tasks', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 取消任务
  async cancelTask(taskId: string): Promise<void> {
    return await api.post(`/tasks/${taskId}/cancel`)
  },

  // 删除任务
  async deleteTask(taskId: string): Promise<void> {
    return await api.delete(`/tasks/${taskId}`)
  },

  // 重试任务
  async retryTask(taskId: string): Promise<Task> {
    return await api.post(`/tasks/${taskId}/retry`)
  },

  // 下载任务结果
  async downloadTaskResult(taskId: string): Promise<Blob> {
    return await api.get(`/tasks/${taskId}/download`, {
      responseType: 'blob'
    })
  },

  // 获取任务统计
  async getTaskStats(): Promise<{
    total: number
    completed: number
    processing: number
    failed: number
  }> {
    return await api.get('/tasks/stats')
  }
}
