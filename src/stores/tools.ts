import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { toolsService } from '@/services'
import type { AITool, ToolCategory } from '@/types'

export const useToolsStore = defineStore('tools', () => {
  const tools = ref<AITool[]>([])
  const loading = ref(false)
  const selectedCategory = ref<ToolCategory | 'all'>('all')

  // 计算属性
  const filteredTools = computed(() => {
    if (selectedCategory.value === 'all') {
      return tools.value
    }
    return tools.value.filter(tool => tool.category === selectedCategory.value)
  })

  const toolsByCategory = computed(() => {
    const categories: Record<ToolCategory | 'all', AITool[]> = {
      all: tools.value,
      image: [],
      video: [],
      audio: [],
      '3d': [],
      text: []
    }

    tools.value.forEach(tool => {
      categories[tool.category].push(tool)
    })

    return categories
  })

  const popularTools = computed(() => {
    return tools.value.filter(tool => tool.isPopular)
  })

  // 获取所有工具
  const fetchTools = async () => {
    loading.value = true
    try {
      tools.value = await toolsService.getTools()
    } catch (error: any) {
      console.error('Failed to fetch tools:', error)
      ElMessage.error('获取工具列表失败')
    } finally {
      loading.value = false
    }
  }

  // 根据分类获取工具
  const fetchToolsByCategory = async (category: ToolCategory) => {
    loading.value = true
    try {
      const categoryTools = await toolsService.getToolsByCategory(category)
      // 更新对应分类的工具
      tools.value = tools.value.filter(tool => tool.category !== category).concat(categoryTools)
    } catch (error: any) {
      console.error('Failed to fetch tools by category:', error)
      ElMessage.error('获取工具列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取工具详情
  const getToolById = (toolId: string): AITool | undefined => {
    return tools.value.find(tool => tool.id === toolId)
  }

  // 设置选中的分类
  const setSelectedCategory = (category: ToolCategory | 'all') => {
    selectedCategory.value = category
  }

  return {
    tools,
    loading,
    selectedCategory,
    filteredTools,
    toolsByCategory,
    popularTools,
    fetchTools,
    fetchToolsByCategory,
    getToolById,
    setSelectedCategory
  }
})
