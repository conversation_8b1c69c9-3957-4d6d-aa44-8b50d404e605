import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { tasksService } from '@/services'
import type { 
  Task, 
  TaskListParams, 
  CreateTaskRequest, 
  TaskStatus, 
  ToolCategory 
} from '@/types'

export const useTasksStore = defineStore('tasks', () => {
  const tasks = ref<Task[]>([])
  const loading = ref(false)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)
  
  // 筛选参数
  const filters = ref<{
    status?: TaskStatus
    toolCategory?: ToolCategory
    search?: string
  }>({})

  // 计算属性
  const completedTasks = computed(() => {
    return tasks.value.filter(task => task.status === 'completed')
  })

  const processingTasks = computed(() => {
    return tasks.value.filter(task => task.status === 'processing')
  })

  const failedTasks = computed(() => {
    return tasks.value.filter(task => task.status === 'failed')
  })

  const recentTasks = computed(() => {
    return tasks.value
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5)
  })

  const taskStats = computed(() => {
    const stats = {
      total: tasks.value.length,
      completed: 0,
      processing: 0,
      failed: 0,
      cancelled: 0
    }

    tasks.value.forEach(task => {
      stats[task.status]++
    })

    return stats
  })

  // 获取任务列表
  const fetchTasks = async (params?: TaskListParams) => {
    loading.value = true
    try {
      const queryParams = {
        page: currentPage.value,
        limit: pageSize.value,
        ...filters.value,
        ...params
      }

      const response = await tasksService.getTasks(queryParams)
      tasks.value = response.tasks
      total.value = response.total
    } catch (error: any) {
      console.error('Failed to fetch tasks:', error)
      ElMessage.error('获取任务列表失败')
    } finally {
      loading.value = false
    }
  }

  // 创建新任务
  const createTask = async (data: CreateTaskRequest): Promise<Task | null> => {
    try {
      const task = await tasksService.createTask(data)
      tasks.value.unshift(task) // 添加到列表开头
      ElMessage.success('任务创建成功')
      return task
    } catch (error: any) {
      console.error('Failed to create task:', error)
      ElMessage.error(error.response?.data?.message || '任务创建失败')
      return null
    }
  }

  // 取消任务
  const cancelTask = async (taskId: string) => {
    try {
      await tasksService.cancelTask(taskId)
      
      // 更新本地状态
      const taskIndex = tasks.value.findIndex(task => task.id === taskId)
      if (taskIndex !== -1) {
        tasks.value[taskIndex].status = 'cancelled'
      }
      
      ElMessage.success('任务已取消')
    } catch (error: any) {
      console.error('Failed to cancel task:', error)
      ElMessage.error('取消任务失败')
    }
  }

  // 删除任务
  const deleteTask = async (taskId: string) => {
    try {
      await tasksService.deleteTask(taskId)
      
      // 从列表中移除
      const taskIndex = tasks.value.findIndex(task => task.id === taskId)
      if (taskIndex !== -1) {
        tasks.value.splice(taskIndex, 1)
      }
      
      ElMessage.success('任务已删除')
    } catch (error: any) {
      console.error('Failed to delete task:', error)
      ElMessage.error('删除任务失败')
    }
  }

  // 重试任务
  const retryTask = async (taskId: string) => {
    try {
      const newTask = await tasksService.retryTask(taskId)
      
      // 更新任务状态
      const taskIndex = tasks.value.findIndex(task => task.id === taskId)
      if (taskIndex !== -1) {
        tasks.value[taskIndex] = newTask
      }
      
      ElMessage.success('任务重试成功')
    } catch (error: any) {
      console.error('Failed to retry task:', error)
      ElMessage.error('重试任务失败')
    }
  }

  // 更新任务状态（用于实时更新）
  const updateTaskStatus = (taskId: string, status: TaskStatus, progress?: number, result?: any) => {
    const taskIndex = tasks.value.findIndex(task => task.id === taskId)
    if (taskIndex !== -1) {
      tasks.value[taskIndex].status = status
      if (progress !== undefined) {
        tasks.value[taskIndex].progress = progress
      }
      if (result) {
        tasks.value[taskIndex].result = result
      }
      if (status === 'completed') {
        tasks.value[taskIndex].completedAt = new Date().toISOString()
      }
    }
  }

  // 设置筛选条件
  const setFilters = (newFilters: typeof filters.value) => {
    filters.value = { ...filters.value, ...newFilters }
    currentPage.value = 1
    fetchTasks()
  }

  // 重置筛选条件
  const resetFilters = () => {
    filters.value = {}
    currentPage.value = 1
    fetchTasks()
  }

  // 设置分页
  const setPage = (page: number) => {
    currentPage.value = page
    fetchTasks()
  }

  const setPageSize = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    fetchTasks()
  }

  return {
    tasks,
    loading,
    total,
    currentPage,
    pageSize,
    filters,
    completedTasks,
    processingTasks,
    failedTasks,
    recentTasks,
    taskStats,
    fetchTasks,
    createTask,
    cancelTask,
    deleteTask,
    retryTask,
    updateTaskStatus,
    setFilters,
    resetFilters,
    setPage,
    setPageSize
  }
})
