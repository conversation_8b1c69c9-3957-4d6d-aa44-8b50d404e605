import api from '@/utils/api'
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type {
  User,
  LoginRequest,
  RegisterRequest,
  LoginResponse,
  RegisterResponse,
  UpdateProfileRequest
} from '@/types'

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const loading = ref(false)

  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isModerator = computed(() => user.value?.role === 'moderator' || isAdmin.value)

  // 登录
  const login = async (credentials: LoginRequest): Promise<LoginResponse> => {
    loading.value = true
    try {
      const response: LoginResponse = await api.post('/auth/login', credentials)

      if (response.success) {
        token.value = response.token
        user.value = response.user

        localStorage.setItem('token', response.token)
        ElMessage.success('登录成功')

        return response
      } else {
        throw new Error('登录失败')
      }
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '登录失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (userData: RegisterRequest): Promise<RegisterResponse> => {
    loading.value = true
    try {
      const response: RegisterResponse = await api.post('/auth/register', userData)

      if (response.success) {
        ElMessage.success(response.message || '注册成功')
      }

      return response
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '注册失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('token')
    ElMessage.success('已退出登录')
  }

  // 获取用户信息
  const fetchUser = async (): Promise<void> => {
    if (!token.value) return

    loading.value = true
    try {
      const response: User = await api.get('/auth/me')
      user.value = response
    } catch (error: any) {
      console.error('Failed to fetch user:', error)
      if (error.response?.status === 401) {
        logout()
      }
    } finally {
      loading.value = false
    }
  }

  // 更新用户信息
  const updateProfile = async (profileData: UpdateProfileRequest): Promise<User> => {
    loading.value = true
    try {
      const response: User = await api.put('/auth/profile', profileData)

      if (user.value) {
        user.value = { ...user.value, ...response }
      }

      ElMessage.success('个人信息更新成功')
      return response
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '更新失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 初始化时检查用户状态
  const initialize = async () => {
    if (token.value) {
      await fetchUser()
    }
  }

  return {
    user,
    token,
    loading,
    isAuthenticated,
    isAdmin,
    isModerator,
    login,
    register,
    logout,
    fetchUser,
    updateProfile,
    initialize
  }
})