import api from '@/utils/api'
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type {
  User,
  LoginRequest,
  RegisterRequest,
  LoginResponse,
  RegisterResponse,
  UpdateProfileRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest
} from '@/types'

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const loading = ref(false)

  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isModerator = computed(() => user.value?.role === 'moderator' || isAdmin.value)

  // 登录
  const login = async (credentials: LoginRequest): Promise<LoginResponse> => {
    loading.value = true
    try {
      // TODO: 替换为真实的 API 调用
      // const response: LoginResponse = await api.post('/auth/login', credentials)

      // 模拟登录成功（开发阶段）
      await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟网络延迟

      const mockResponse: LoginResponse = {
        success: true,
        token: 'mock-jwt-token-' + Date.now(),
        user: {
          id: 1,
          firstName: credentials.email.split('@')[0].charAt(0).toUpperCase() + credentials.email.split('@')[0].slice(1), // 首字母大写
          lastName: credentials.email.includes('admin') ? 'Admin' : 'User',
          email: credentials.email,
          role: credentials.email.includes('admin') ? 'admin' : 'user', // 如果邮箱包含admin则为管理员
          avatar: undefined, // 不设置头像，让组件显示姓名首字母
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }

      token.value = mockResponse.token
      user.value = mockResponse.user

      localStorage.setItem('token', mockResponse.token)
      ElMessage.success('登录成功')

      return mockResponse
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '登录失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (userData: RegisterRequest): Promise<RegisterResponse> => {
    loading.value = true
    try {
      // TODO: 替换为真实的 API 调用
      // const response: RegisterResponse = await api.post('/auth/register', userData)

      // 模拟注册成功（开发阶段）
      await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟网络延迟

      const mockResponse: RegisterResponse = {
        success: true,
        message: '注册成功，请登录'
      }

      ElMessage.success(mockResponse.message)
      return mockResponse
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '注册失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('token')
    ElMessage.success('已退出登录')
  }

  // 获取用户信息
  const fetchUser = async (): Promise<void> => {
    if (!token.value) return

    loading.value = true
    try {
      const response: User = await api.get('/auth/me')
      user.value = response
    } catch (error: any) {
      console.error('Failed to fetch user:', error)
      if (error.response?.status === 401) {
        logout()
      }
    } finally {
      loading.value = false
    }
  }

  // 更新用户信息
  const updateProfile = async (profileData: UpdateProfileRequest): Promise<User> => {
    loading.value = true
    try {
      const response: User = await api.put('/auth/profile', profileData)

      if (user.value) {
        user.value = { ...user.value, ...response }
      }

      ElMessage.success('个人信息更新成功')
      return response
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '更新失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 忘记密码
  const forgotPassword = async (data: ForgotPasswordRequest): Promise<void> => {
    loading.value = true
    try {
      const response = await api.post('/auth/forgot-password', data)
      ElMessage.success(response.message || '重置密码邮件已发送')
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '发送重置邮件失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 重置密码
  const resetPassword = async (data: ResetPasswordRequest): Promise<void> => {
    loading.value = true
    try {
      const response = await api.post('/auth/reset-password', data)
      ElMessage.success(response.message || '密码重置成功')
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '密码重置失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 初始化时检查用户状态
  const initialize = async () => {
    if (token.value) {
      await fetchUser()
    }
  }

  return {
    user,
    token,
    loading,
    isAuthenticated,
    isAdmin,
    isModerator,
    login,
    register,
    logout,
    fetchUser,
    updateProfile,
    forgotPassword,
    resetPassword,
    initialize
  }
})