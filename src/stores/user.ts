import api from '@/utils/api'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface User {
  id: number
  username: string
  email: string
  avatar?: string
  role: string
}

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const isAuthenticated = ref<boolean>(!!token.value)

  // 登录
  const login = async (credentials: { username: string; password: string }) => {
    try {
      const response: any = await api.post('/auth/login', credentials)
      const { token: newToken, user: userData } = response
      
      token.value = newToken
      user.value = userData
      isAuthenticated.value = true
      
      localStorage.setItem('token', newToken)
      
      return { success: true }
    } catch (error) {
      return { success: false, error }
    }
  }

  // 注册
  const register = async (userData: { username: string; email: string; password: string }) => {
    try {
      const response = await api.post('/auth/register', userData)
      return { success: true, data: response }
    } catch (error) {
      return { success: false, error }
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = null
    isAuthenticated.value = false
    localStorage.removeItem('token')
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response: any = await api.get('/auth/me')
      user.value = response
      return { success: true }
    } catch (error) {
      logout()
      return { success: false, error }
    }
  }

  // 更新用户信息
  const updateUserInfo = async (userData: Partial<User>) => {
    try {
      const response: any = await api.put('/auth/profile', userData)
      user.value = { ...user.value, ...response }
      return { success: true }
    } catch (error) {
      return { success: false, error }
    }
  }

  return {
    user,
    token,
    isAuthenticated,
    login,
    register,
    logout,
    fetchUserInfo,
    updateUserInfo,
  }
}) 