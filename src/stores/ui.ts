import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useUIStore = defineStore('ui', () => {
  // 模态框状态
  const modals = ref<Record<string, boolean>>({
    toolModal: false,
    profileModal: false,
    settingsModal: false
  })

  // 侧边栏状态
  const sidebarCollapsed = ref(false)

  // 主题设置
  const theme = ref<'light' | 'dark' | 'auto'>('light')

  // 加载状态
  const globalLoading = ref(false)

  // 通知状态
  const notifications = ref<Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message: string
    duration?: number
  }>>([])

  // 面包屑导航
  const breadcrumbs = ref<Array<{
    name: string
    path?: string
  }>>([])

  // 模态框操作
  const openModal = (modalName: string) => {
    modals.value[modalName] = true
  }

  const closeModal = (modalName: string) => {
    modals.value[modalName] = false
  }

  const toggleModal = (modalName: string) => {
    modals.value[modalName] = !modals.value[modalName]
  }

  // 侧边栏操作
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
  }

  // 主题操作
  const setTheme = (newTheme: 'light' | 'dark' | 'auto') => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    
    // 应用主题到 document
    if (newTheme === 'dark') {
      document.documentElement.classList.add('dark')
    } else if (newTheme === 'light') {
      document.documentElement.classList.remove('dark')
    } else {
      // auto - 根据系统偏好
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      if (prefersDark) {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }
    }
  }

  // 初始化主题
  const initTheme = () => {
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | 'auto' || 'light'
    setTheme(savedTheme)
  }

  // 全局加载状态
  const setGlobalLoading = (loading: boolean) => {
    globalLoading.value = loading
  }

  // 通知操作
  const addNotification = (notification: {
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message: string
    duration?: number
  }) => {
    const id = Date.now().toString()
    const newNotification = {
      id,
      duration: 5000,
      ...notification
    }
    
    notifications.value.push(newNotification)
    
    // 自动移除通知
    if (newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id)
      }, newNotification.duration)
    }
    
    return id
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearNotifications = () => {
    notifications.value = []
  }

  // 面包屑操作
  const setBreadcrumbs = (crumbs: Array<{ name: string; path?: string }>) => {
    breadcrumbs.value = crumbs
  }

  const addBreadcrumb = (crumb: { name: string; path?: string }) => {
    breadcrumbs.value.push(crumb)
  }

  const clearBreadcrumbs = () => {
    breadcrumbs.value = []
  }

  return {
    modals,
    sidebarCollapsed,
    theme,
    globalLoading,
    notifications,
    breadcrumbs,
    openModal,
    closeModal,
    toggleModal,
    toggleSidebar,
    setSidebarCollapsed,
    setTheme,
    initTheme,
    setGlobalLoading,
    addNotification,
    removeNotification,
    clearNotifications,
    setBreadcrumbs,
    addBreadcrumb,
    clearBreadcrumbs
  }
})
