// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  avatar?: string
  role: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
}

export interface LoginResponse {
  success: boolean
  token: string
  user: User
}

export interface RegisterResponse {
  success: boolean
  message: string
}

export interface UpdateProfileRequest {
  username?: string
  email?: string
  avatar?: string
}

// 视频相关类型
export interface Video {
  id: number
  title: string
  description: string
  thumbnail: string
  videoUrl: string
  duration: number
  views: number
  likes: number
  createdAt: string
  author?: {
    id: number
    username: string
    avatar: string
  }
}

export interface VideoListResponse {
  videos: Video[]
  total: number
  page: number
  limit: number
}

export interface VideoListParams {
  page?: number
  limit?: number
  search?: string
  category?: string
}

export interface UploadVideoRequest {
  file: File
  title: string
  description: string
  category: string
}

export interface UpdateVideoRequest {
  title?: string
  description?: string
  category?: string
}

// 播放列表相关类型
export interface Playlist {
  id: number
  name: string
  description: string
  thumbnail?: string
  videoCount: number
  createdAt: string
}

export interface PlaylistListResponse {
  playlists: Playlist[]
}

export interface CreatePlaylistRequest {
  name: string
  description: string
}

// 数据分析相关类型
export interface AnalyticsStats {
  totalVideos: number
  totalViews: number
  totalLikes: number
  followers: number
  monthlyViews: MonthlyView[]
}

export interface MonthlyView {
  month: string
  views: number
}

// API 响应基础类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  code?: number
}

// 分页相关类型
export interface PaginationParams {
  page?: number
  limit?: number
}

export interface PaginationResponse {
  total: number
  page: number
  limit: number
  pages?: number
}

// 错误类型
export interface ApiError {
  message: string
  code: number
  status: number
}

// 表单验证规则类型
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: any) => void
}

export interface FormRules {
  [key: string]: FormRule[]
}

// 菜单项类型
export interface MenuItem {
  name: string
  path: string
  icon?: string
  children?: MenuItem[]
  meta?: {
    requiresAuth?: boolean
    title?: string
  }
}

// 上传文件类型
export interface UploadFile {
  name: string
  size: number
  type: string
  url?: string
  status?: 'ready' | 'uploading' | 'success' | 'error'
  percentage?: number
}

// 视频分类类型
export type VideoCategory = 
  | 'education'
  | 'entertainment'
  | 'music'
  | 'sports'
  | 'technology'
  | 'gaming'
  | 'news'
  | 'lifestyle'
  | 'other'

// 用户角色类型
export type UserRole = 'admin' | 'user' | 'moderator'

// 排序类型
export type SortOrder = 'asc' | 'desc'

export interface SortParams {
  field: string
  order: SortOrder
}

// 搜索过滤器类型
export interface SearchFilters {
  category?: VideoCategory
  dateRange?: [string, string]
  minDuration?: number
  maxDuration?: number
  sortBy?: 'createdAt' | 'views' | 'likes' | 'title'
  sortOrder?: SortOrder
}
