// 用户相关类型
export interface User {
  id: number
  firstName: string
  lastName: string
  email: string
  avatar?: string
  role: 'user' | 'admin' | 'moderator'
  createdAt: string
  updatedAt: string
  subscription?: Subscription
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string
  terms: boolean
}

export interface LoginResponse {
  success: boolean
  token: string
  user: User
}

export interface RegisterResponse {
  success: boolean
  message: string
}

export interface UpdateProfileRequest {
  firstName?: string
  lastName?: string
  email?: string
  avatar?: string
}

export interface ForgotPasswordRequest {
  email: string
}

export interface ResetPasswordRequest {
  token: string
  password: string
  confirmPassword: string
}

// AI 工具相关类型
export interface AITool {
  id: string
  name: string
  description: string
  category: ToolCategory
  icon: string
  costPerUse: number
  isPopular?: boolean
  isFast?: boolean
  isPremium?: boolean
  isNew?: boolean
  isBeta?: boolean
  config: ToolConfig
}

export type ToolCategory = 'image' | 'video' | 'audio' | '3d' | 'text'

export interface ToolConfig {
  maxFileSize?: number
  supportedFormats?: string[]
  parameters: ToolParameter[]
}

export interface ToolParameter {
  name: string
  type: 'text' | 'number' | 'select' | 'file' | 'boolean'
  label: string
  description?: string
  required: boolean
  defaultValue?: any
  options?: { label: string; value: any }[]
  min?: number
  max?: number
}

// 任务相关类型
export interface Task {
  id: string
  userId: number
  toolId: string
  toolName: string
  status: TaskStatus
  prompt?: string
  parameters: Record<string, any>
  result?: TaskResult
  error?: string
  progress: number
  creditsUsed: number
  createdAt: string
  updatedAt: string
  completedAt?: string
}

export type TaskStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'

export interface TaskResult {
  type: 'image' | 'video' | 'audio' | '3d' | 'text'
  url?: string
  urls?: string[]
  metadata?: Record<string, any>
}

export interface CreateTaskRequest {
  toolId: string
  prompt?: string
  parameters: Record<string, any>
  files?: File[]
}

export interface TaskListResponse {
  tasks: Task[]
  total: number
  page: number
  limit: number
}

export interface TaskListParams {
  page?: number
  limit?: number
  status?: TaskStatus
  toolCategory?: ToolCategory
  search?: string
}

// 订阅和计费相关类型
export interface Subscription {
  id: string
  planId: string
  planName: string
  status: 'active' | 'cancelled' | 'expired' | 'trial'
  creditsRemaining: number
  creditsTotal: number
  renewsAt: string
  cancelledAt?: string
}

export interface PricingPlan {
  id: string
  name: string
  description: string
  price: {
    monthly: number
    yearly: number
  }
  credits: number
  features: string[]
  isPopular?: boolean
  isEnterprise?: boolean
}

export interface BillingInfo {
  subscription: Subscription
  usage: UsageStats
  invoices: Invoice[]
  paymentMethods: PaymentMethod[]
}

export interface UsageStats {
  currentPeriod: {
    creditsUsed: number
    tasksCompleted: number
    startDate: string
    endDate: string
  }
  history: {
    date: string
    creditsUsed: number
    tasksCompleted: number
  }[]
}

export interface Invoice {
  id: string
  amount: number
  currency: string
  status: 'paid' | 'pending' | 'failed'
  createdAt: string
  paidAt?: string
  downloadUrl?: string
}

export interface PaymentMethod {
  id: string
  type: 'card' | 'paypal'
  last4?: string
  brand?: string
  expiryMonth?: number
  expiryYear?: number
  isDefault: boolean
}

// 管理员相关类型
export interface AdminStats {
  totalUsers: number
  activeUsers: number
  totalTasks: number
  completedTasks: number
  totalCreditsUsed: number
  revenue: number
  systemHealth: {
    cpu: number
    memory: number
    storage: number
    uptime: string
  }
}

export interface AdminUser {
  id: number
  firstName: string
  lastName: string
  email: string
  role: string
  status: 'active' | 'suspended' | 'banned'
  subscription?: Subscription
  totalCreditsUsed: number
  lastLoginAt?: string
  createdAt: string
}

// API 响应基础类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  code?: number
}

// 分页相关类型
export interface PaginationParams {
  page?: number
  limit?: number
}

export interface PaginationResponse {
  total: number
  page: number
  limit: number
  pages?: number
}

// 错误类型
export interface ApiError {
  message: string
  code: number
  status: number
}

// 表单验证规则类型
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: any) => void
}

export interface FormRules {
  [key: string]: FormRule[]
}

// 菜单项类型
export interface MenuItem {
  name: string
  path: string
  icon?: string
  children?: MenuItem[]
  meta?: {
    requiresAuth?: boolean
    requiresAdmin?: boolean
    title?: string
  }
}

// 上传文件类型
export interface UploadFile {
  name: string
  size: number
  type: string
  url?: string
  status?: 'ready' | 'uploading' | 'success' | 'error'
  percentage?: number
}

// 通知类型
export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  read: boolean
  createdAt: string
}

// 设置类型
export interface UserSettings {
  notifications: {
    email: boolean
    push: boolean
    taskComplete: boolean
    taskFailed: boolean
    billing: boolean
  }
  preferences: {
    theme: 'light' | 'dark' | 'auto'
    language: string
    timezone: string
  }
  privacy: {
    profilePublic: boolean
    showEmail: boolean
  }
}

// 排序类型
export type SortOrder = 'asc' | 'desc'

export interface SortParams {
  field: string
  order: SortOrder
}
