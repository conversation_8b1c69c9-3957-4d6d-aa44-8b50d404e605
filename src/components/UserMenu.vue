<template>
  <el-dropdown @command="handleCommand" trigger="click">
    <div class="flex items-center cursor-pointer hover:bg-gray-50 rounded-lg p-2 transition-colors duration-200">
      <UserAvatar
        :first-name="userStore.user?.firstName"
        :last-name="userStore.user?.lastName"
        :avatar="userStore.user?.avatar"
        size="sm"
      />
      <div class="ml-2 hidden sm:block">
        <p class="text-sm font-medium text-gray-900">{{ userFullName }}</p>
        <p class="text-xs text-gray-500">{{ userStore.user?.email }}</p>
      </div>
      <el-icon class="ml-2 text-gray-400">
        <ArrowDown />
      </el-icon>
    </div>
    
    <template #dropdown>
      <el-dropdown-menu>
        <!-- 用户信息 -->
        <div class="px-4 py-3 border-b border-gray-100">
          <div class="flex items-center">
            <UserAvatar
              :first-name="userStore.user?.firstName"
              :last-name="userStore.user?.lastName"
              :avatar="userStore.user?.avatar"
              size="md"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">{{ userFullName }}</p>
              <p class="text-xs text-gray-500">{{ userStore.user?.email }}</p>
            </div>
          </div>
          
          <!-- 订阅信息 -->
          <div v-if="userStore.user?.subscription" class="mt-3 p-2 bg-blue-50 rounded-lg">
            <div class="flex items-center justify-between">
              <span class="text-xs font-medium text-blue-800">
                {{ userStore.user.subscription.planName }}
              </span>
              <span class="text-xs text-blue-600">
                {{ userStore.user.subscription.creditsRemaining }} 积分剩余
              </span>
            </div>
            <div class="mt-1 bg-blue-200 rounded-full h-1">
              <div
                class="bg-blue-600 h-1 rounded-full transition-all duration-300"
                :style="{ 
                  width: `${(userStore.user.subscription.creditsRemaining / userStore.user.subscription.creditsTotal) * 100}%` 
                }"
              ></div>
            </div>
          </div>
        </div>

        <!-- 菜单项 -->
        <el-dropdown-item command="profile">
          <el-icon><User /></el-icon>
          {{ t('userMenu.profile') }}
        </el-dropdown-item>

        <el-dropdown-item command="settings">
          <el-icon><Setting /></el-icon>
          {{ t('userMenu.settings') }}
        </el-dropdown-item>

        <!-- 主题切换 -->
        <el-dropdown-item divided>
          <div class="flex items-center justify-between w-full">
            <div class="flex items-center">
              <el-icon><Sunny /></el-icon>
              <span class="ml-2">{{ t('userMenu.theme') }}</span>
            </div>
            <el-select
              :model-value="uiStore.theme"
              @update:model-value="uiStore.setTheme"
              size="small"
              style="width: 80px"
            >
              <el-option :label="t('theme.light')" value="light" />
              <el-option :label="t('theme.dark')" value="dark" />
              <el-option :label="t('theme.auto')" value="auto" />
            </el-select>
          </div>
        </el-dropdown-item>

        <!-- 语言切换 -->
        <el-dropdown-item>
          <div class="flex items-center justify-between w-full">
            <div class="flex items-center">
              <el-icon><Operation /></el-icon>
              <span class="ml-2">{{ t('userMenu.language') }}</span>
            </div>
            <el-select
              :model-value="getCurrentLocale()"
              @update:model-value="setLocale"
              size="small"
              style="width: 80px"
            >
              <el-option
                v-for="locale in supportedLocales"
                :key="locale.code"
                :label="locale.flag + ' ' + locale.name"
                :value="locale.code"
              />
            </el-select>
          </div>
        </el-dropdown-item>

        <!-- 帮助和支持 -->
        <el-dropdown-item command="help">
          <el-icon><QuestionFilled /></el-icon>
          {{ t('userMenu.help') }}
        </el-dropdown-item>

        <!-- 退出登录 -->
        <el-dropdown-item divided command="logout">
          <el-icon><SwitchButton /></el-icon>
          {{ t('nav.logout') }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessageBox } from 'element-plus'
import {
  ArrowDown,
  House,
  List,
  User,
  Setting,
  CreditCard,
  Tools,
  Sunny,
  Operation,
  QuestionFilled,
  SwitchButton
} from '@element-plus/icons-vue'
import UserAvatar from './UserAvatar.vue'
import { useUserStore } from '@/stores/user'
import { useUIStore } from '@/stores/ui'
import { getCurrentLocale, setLocale, supportedLocales } from '@/i18n'

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()
const uiStore = useUIStore()

const userFullName = computed(() => {
  if (!userStore.user) return ''
  return `${userStore.user.firstName} ${userStore.user.lastName}`.trim()
})

const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'help':
      // 打开帮助页面或模态框
      window.open('https://help.example.com', '_blank')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '确认退出',
          {
            confirmButtonText: '退出',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        
        userStore.logout()
        router.push('/')
      } catch (error) {
        // 用户取消退出
      }
      break
  }
}
</script>
