<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <AppNavigation />
    
    <!-- 主要内容区域 -->
    <main class="pt-16">
      <slot />
    </main>
    
    <!-- 全局通知 -->
    <div class="fixed top-20 right-4 z-50 space-y-2">
      <transition-group name="notification" tag="div">
        <div
          v-for="notification in uiStore.notifications"
          :key="notification.id"
          :class="[
            'max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden',
            {
              'border-l-4 border-green-400': notification.type === 'success',
              'border-l-4 border-red-400': notification.type === 'error',
              'border-l-4 border-yellow-400': notification.type === 'warning',
              'border-l-4 border-blue-400': notification.type === 'info'
            }
          ]"
        >
          <div class="p-4">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <el-icon 
                  :class="[
                    'h-6 w-6',
                    {
                      'text-green-400': notification.type === 'success',
                      'text-red-400': notification.type === 'error',
                      'text-yellow-400': notification.type === 'warning',
                      'text-blue-400': notification.type === 'info'
                    }
                  ]"
                >
                  <Check v-if="notification.type === 'success'" />
                  <Close v-else-if="notification.type === 'error'" />
                  <Warning v-else-if="notification.type === 'warning'" />
                  <InfoFilled v-else />
                </el-icon>
              </div>
              <div class="ml-3 w-0 flex-1 pt-0.5">
                <p class="text-sm font-medium text-gray-900">
                  {{ notification.title }}
                </p>
                <p class="mt-1 text-sm text-gray-500">
                  {{ notification.message }}
                </p>
              </div>
              <div class="ml-4 flex-shrink-0 flex">
                <button
                  @click="uiStore.removeNotification(notification.id)"
                  class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <el-icon class="h-5 w-5">
                    <Close />
                  </el-icon>
                </button>
              </div>
            </div>
          </div>
        </div>
      </transition-group>
    </div>
    
    <!-- 全局加载遮罩 -->
    <div
      v-if="uiStore.globalLoading"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
        <el-icon class="animate-spin h-6 w-6 text-blue-600">
          <Loading />
        </el-icon>
        <span class="text-gray-900">加载中...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { 
  Check, 
  Close, 
  Warning, 
  InfoFilled, 
  Loading 
} from '@element-plus/icons-vue'
import AppNavigation from './AppNavigation.vue'
import { useUIStore } from '@/stores/ui'

const uiStore = useUIStore()

onMounted(() => {
  // 初始化主题
  uiStore.initTheme()
})
</script>

<style scoped>
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}
</style>
