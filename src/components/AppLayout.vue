<template>
  <div class="min-h-screen bg-gray-50">
    <AppHeader />
    
    <div class="flex">
      <AppSidebar v-if="showSidebar" />
      
      <main class="flex-1 overflow-auto">
        <div class="p-6">
          <slot />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import AppHeader from './AppHeader.vue'
import AppSidebar from './AppSidebar.vue'

const route = useRoute()

// 在某些页面不显示侧边栏
const showSidebar = computed(() => {
  const hideSidebarRoutes = ['/login', '/register']
  return !hideSidebarRoutes.includes(route.path)
})
</script>
