<template>
  <div
    :class="[
      'flex items-center justify-center rounded-full font-medium text-white',
      sizeClasses,
      backgroundClass
    ]"
  >
    <!-- 如果有头像图片，显示图片 -->
    <img
      v-if="avatar"
      :src="avatar"
      :alt="name"
      class="w-full h-full rounded-full object-cover"
    />
    <!-- 否则显示姓名首字母 -->
    <span v-else :class="textSizeClass">
      {{ initials }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  firstName?: string
  lastName?: string
  name?: string // 备用，如果没有 firstName 和 lastName
  avatar?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
}

const props = withDefaults(defineProps<Props>(), {
  firstName: '',
  lastName: '',
  name: '',
  avatar: '',
  size: 'md'
})

// 计算姓名首字母
const initials = computed(() => {
  if (props.firstName && props.lastName) {
    return (props.firstName.charAt(0) + props.lastName.charAt(0)).toUpperCase()
  } else if (props.name) {
    const nameParts = props.name.trim().split(' ')
    if (nameParts.length >= 2) {
      return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase()
    } else if (nameParts.length === 1) {
      return nameParts[0].charAt(0).toUpperCase()
    }
  } else if (props.firstName) {
    return props.firstName.charAt(0).toUpperCase()
  } else if (props.lastName) {
    return props.lastName.charAt(0).toUpperCase()
  }
  return 'U' // 默认显示 U (User)
})

// 根据姓名生成背景颜色
const backgroundClass = computed(() => {
  const colors = [
    'bg-red-500',
    'bg-orange-500',
    'bg-amber-500',
    'bg-yellow-500',
    'bg-lime-500',
    'bg-green-500',
    'bg-emerald-500',
    'bg-teal-500',
    'bg-cyan-500',
    'bg-sky-500',
    'bg-blue-500',
    'bg-indigo-500',
    'bg-violet-500',
    'bg-purple-500',
    'bg-fuchsia-500',
    'bg-pink-500',
    'bg-rose-500'
  ]
  
  // 基于姓名生成一个稳定的颜色索引
  const name = props.firstName + props.lastName + props.name
  let hash = 0
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash)
  }
  const index = Math.abs(hash) % colors.length
  return colors[index]
})

// 尺寸类
const sizeClasses = computed(() => {
  const sizes = {
    xs: 'w-6 h-6',
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16',
    '2xl': 'w-20 h-20'
  }
  return sizes[props.size]
})

// 文字尺寸类
const textSizeClass = computed(() => {
  const textSizes = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg',
    '2xl': 'text-xl'
  }
  return textSizes[props.size]
})
</script>
