<template>
  <nav class="bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-40">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo 和品牌 -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center">
            <div class="h-8 w-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">AI</span>
            </div>
            <span class="ml-2 text-xl font-bold text-gray-900">AI Generator</span>
          </router-link>
        </div>

        <!-- 中间导航菜单 -->
        <div class="hidden sm:flex items-center space-x-4 md:space-x-8">
          <router-link
            v-for="item in navigationItems"
            :key="item.name"
            :to="item.path"
            v-show="item.public || (item.requiresAuth && userStore.isAuthenticated) && (!item.requiresAdmin || userStore.isAdmin)"
            :class="[
              'px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-colors duration-200',
              $route.path === item.path
                ? 'text-blue-600 bg-blue-50'
                : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
            ]"
          >
            {{ item.name }}
          </router-link>
        </div>

        <!-- 右侧用户菜单 -->
        <div class="flex items-center space-x-2 sm:space-x-4">
          <!-- 未登录状态 -->
          <template v-if="!userStore.isAuthenticated">
            <!-- 语言选择器 -->
            <el-select
              :model-value="getCurrentLocale()"
              @update:model-value="setLocale"
              size="small"
              style="width: 120px"
              class="hidden sm:block"
            >
              <el-option
                v-for="locale in supportedLocales"
                :key="locale.code"
                :label="locale.flag + ' ' + locale.name"
                :value="locale.code"
              />
            </el-select>

            <router-link
              to="/login"
              class="text-gray-700 hover:text-blue-600 px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium"
            >
              {{ t('nav.login') }}
            </router-link>
            <router-link
              to="/register"
              class="bg-blue-600 hover:bg-blue-700 text-white px-3 sm:px-4 py-2 rounded-md text-xs sm:text-sm font-medium transition-colors duration-200"
            >
              {{ t('nav.register') }}
            </router-link>
          </template>

          <!-- 已登录状态 -->
          <template v-else>
            <!-- 语言选择器 -->
            <el-select
              :model-value="getCurrentLocale()"
              @update:model-value="setLocale"
              size="small"
              style="width: 120px"
              class="hidden lg:block"
            >
              <el-option
                v-for="locale in supportedLocales"
                :key="locale.code"
                :label="locale.flag + ' ' + locale.name"
                :value="locale.code"
              />
            </el-select>

            <!-- 通知按钮 -->
            <el-badge :value="unreadNotifications" :hidden="unreadNotifications === 0">
              <el-button :icon="Bell" circle @click="showNotifications = !showNotifications" />
            </el-badge>

            <!-- 用户菜单 -->
            <UserMenu />
          </template>


        </div>
      </div>
    </div>



    <!-- 通知面板 -->
    <div
      v-if="showNotifications && userStore.isAuthenticated"
      class="absolute top-16 right-4 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
    >
      <div class="p-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">{{ t('common.notifications', 'Notifications') }}</h3>
          <el-button size="small" text @click="markAllAsRead">
            {{ t('notifications.markAllRead') }}
          </el-button>
        </div>
      </div>
      
      <div class="max-h-96 overflow-y-auto">
        <div v-if="notifications.length === 0" class="p-4 text-center text-gray-500">
          {{ t('notifications.noNotifications') }}
        </div>
        <div
          v-for="notification in notifications"
          :key="notification.id"
          :class="[
            'p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer',
            { 'bg-blue-50': !notification.read }
          ]"
          @click="markAsRead(notification.id)"
        >
          <div class="flex items-start">
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900">
                {{ notification.title }}
              </p>
              <p class="text-sm text-gray-600 mt-1">
                {{ notification.message }}
              </p>
              <p class="text-xs text-gray-400 mt-2">
                {{ formatDate(notification.createdAt) }}
              </p>
            </div>
            <div v-if="!notification.read" class="w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1"></div>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { Bell } from '@element-plus/icons-vue'
import UserMenu from './UserMenu.vue'
import { useUserStore } from '@/stores/user'
import { userService } from '@/services'
import { getCurrentLocale, setLocale, supportedLocales } from '@/i18n'
import type { Notification } from '@/types'

const { t } = useI18n()
const userStore = useUserStore()

const showNotifications = ref(false)
const notifications = ref<Notification[]>([])

const navigationItems = computed(() => [
  { name: t('nav.home'), path: '/', public: true },
  { name: t('nav.dashboard'), path: '/dashboard', requiresAuth: true },
  { name: t('nav.tools'), path: '/tools', public: true },
  { name: t('nav.tasks'), path: '/tasks', requiresAuth: true },
  { name: t('nav.pricing'), path: '/pricing', public: true },
  { name: t('nav.admin'), path: '/admin', requiresAuth: true, requiresAdmin: true }
])

const unreadNotifications = computed(() => {
  return notifications.value.filter(n => !n.read).length
})

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) return '1天前'
  if (diffDays < 7) return `${diffDays}天前`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`
  return `${Math.floor(diffDays / 30)}个月前`
}

const fetchNotifications = async () => {
  if (!userStore.isAuthenticated) return

  try {
    // TODO: 实现通知功能时启用
    // notifications.value = await userService.getNotifications()
    notifications.value = [] // 临时返回空数组
  } catch (error) {
    console.error('Failed to fetch notifications:', error)
  }
}

const markAsRead = async (notificationId: string) => {
  try {
    // TODO: 实现通知功能时启用
    // await userService.markNotificationAsRead(notificationId)
    const notification = notifications.value.find(n => n.id === notificationId)
    if (notification) {
      notification.read = true
    }
  } catch (error) {
    console.error('Failed to mark notification as read:', error)
  }
}

const markAllAsRead = async () => {
  try {
    // TODO: 实现通知功能时启用
    // await userService.markAllNotificationsAsRead()
    notifications.value.forEach(n => n.read = true)
  } catch (error) {
    console.error('Failed to mark all notifications as read:', error)
  }
}

// 点击外部关闭通知面板
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.notification-panel') && !target.closest('.notification-button')) {
    showNotifications.value = false
  }
}

onMounted(() => {
  fetchNotifications()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
