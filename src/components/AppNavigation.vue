<template>
  <nav class="bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-40">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- <PERSON><PERSON> 和品牌 -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center">
            <div class="h-8 w-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">WV</span>
            </div>
            <span class="ml-2 text-xl font-bold text-gray-900">{{ t('app.name') }}</span>
          </router-link>
        </div>

        <!-- 中间导航菜单 -->
        <div class="hidden sm:flex items-center space-x-4 md:space-x-8">
          <router-link
            v-for="item in navigationItems"
            :key="item.name"
            :to="item.path"
            v-show="item.public || (item.requiresAuth && userStore.isAuthenticated) && (!item.requiresAdmin || userStore.isAdmin)"
            :class="[
              'px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-colors duration-200',
              $route.path === item.path
                ? 'text-blue-600 bg-blue-50'
                : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
            ]"
          >
            {{ item.name }}
          </router-link>
        </div>

        <!-- 右侧用户菜单 -->
        <div class="flex items-center space-x-2 sm:space-x-4">
          <!-- 未登录状态 -->
          <template v-if="!userStore.isAuthenticated">
            <!-- 语言选择器 -->
            <el-select
              :model-value="getCurrentLocale()"
              @update:model-value="setLocale"
              size="small"
              style="width: 120px"
              class="hidden sm:block"
            >
              <el-option
                v-for="locale in supportedLocales"
                :key="locale.code"
                :label="locale.flag + ' ' + locale.name"
                :value="locale.code"
              />
            </el-select>

            <router-link
              to="/login"
              class="text-gray-700 hover:text-blue-600 px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium"
            >
              {{ t('nav.login') }}
            </router-link>
            <router-link
              to="/register"
              class="bg-blue-600 hover:bg-blue-700 text-white px-3 sm:px-4 py-2 rounded-md text-xs sm:text-sm font-medium transition-colors duration-200"
            >
              {{ t('nav.register') }}
            </router-link>
          </template>

          <!-- 已登录状态 -->
          <template v-else>
            <!-- 语言选择器 -->
            <el-select
              :model-value="getCurrentLocale()"
              @update:model-value="setLocale"
              size="small"
              style="width: 120px"
              class="hidden lg:block"
            >
              <el-option
                v-for="locale in supportedLocales"
                :key="locale.code"
                :label="locale.flag + ' ' + locale.name"
                :value="locale.code"
              />
            </el-select>

            <!-- 用户菜单 -->
            <UserMenu />
          </template>


        </div>
      </div>
    </div>




  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import UserMenu from './UserMenu.vue'
import { useUserStore } from '@/stores/user'
import { getCurrentLocale, setLocale, supportedLocales } from '@/i18n'

const { t } = useI18n()
const userStore = useUserStore()

const navigationItems = computed(() => [
  { name: t('nav.home'), path: '/', public: true },
  { name: t('nav.dashboard'), path: '/dashboard', requiresAuth: true },
  { name: t('nav.tools'), path: '/tools', public: true },
  { name: t('nav.tasks'), path: '/tasks', requiresAuth: true },
  { name: t('nav.pricing'), path: '/pricing', public: true },
  { name: t('nav.admin'), path: '/admin', requiresAuth: true, requiresAdmin: true }
])
</script>
