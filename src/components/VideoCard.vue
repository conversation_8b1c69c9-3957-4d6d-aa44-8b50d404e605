<template>
  <div class="video-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
    <div class="relative">
      <img 
        :src="video.thumbnail" 
        :alt="video.title"
        class="w-full h-48 object-cover cursor-pointer"
        @click="$emit('click', video)"
      />
      <div class="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
        {{ formatDuration(video.duration) }}
      </div>
    </div>
    
    <div class="p-4">
      <h3 
        class="font-semibold text-lg mb-2 line-clamp-2 cursor-pointer hover:text-blue-600"
        @click="$emit('click', video)"
      >
        {{ video.title }}
      </h3>
      
      <p class="text-gray-600 text-sm mb-3 line-clamp-2">
        {{ video.description }}
      </p>
      
      <div class="flex items-center justify-between text-sm text-gray-500">
        <div class="flex items-center space-x-4">
          <span class="flex items-center">
            <el-icon class="mr-1"><View /></el-icon>
            {{ formatNumber(video.views) }}
          </span>
          <span class="flex items-center">
            <el-icon class="mr-1"><Star /></el-icon>
            {{ formatNumber(video.likes) }}
          </span>
        </div>
        <span>{{ formatDate(video.createdAt) }}</span>
      </div>
      
      <div v-if="video.author" class="flex items-center mt-3 pt-3 border-t">
        <img 
          :src="video.author.avatar || '/default-avatar.png'" 
          :alt="video.author.username"
          class="w-8 h-8 rounded-full mr-2"
        />
        <span class="text-sm text-gray-700">{{ video.author.username }}</span>
      </div>
      
      <div v-if="showActions" class="flex justify-end mt-3 space-x-2">
        <el-button size="small" @click="$emit('edit', video)">
          <el-icon><Edit /></el-icon>
        </el-button>
        <el-button size="small" type="danger" @click="$emit('delete', video)">
          <el-icon><Delete /></el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { View, Star, Edit, Delete } from '@element-plus/icons-vue'
import type { Video } from '@/types'

interface Props {
  video: Video
  showActions?: boolean
}

defineProps<Props>()

defineEmits<{
  click: [video: Video]
  edit: [video: Video]
  delete: [video: Video]
}>()

const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) return '1天前'
  if (diffDays < 7) return `${diffDays}天前`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`
  if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`
  return `${Math.floor(diffDays / 365)}年前`
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
