<template>
  <header class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <router-link to="/dashboard" class="flex items-center">
            <img src="/logo.svg" alt="Wise Video" class="h-8 w-8 mr-2" />
            <span class="text-xl font-bold text-gray-900">Wise Video</span>
          </router-link>
        </div>

        <!-- Search Bar -->
        <div class="flex-1 max-w-lg mx-8">
          <div class="relative">
            <el-input
              v-model="searchQuery"
              placeholder="搜索视频..."
              class="w-full"
              @keyup.enter="handleSearch"
            >
              <template #suffix>
                <el-button 
                  :icon="Search" 
                  @click="handleSearch"
                  text
                />
              </template>
            </el-input>
          </div>
        </div>

        <!-- User Menu -->
        <div class="flex items-center space-x-4">
          <!-- Upload Button -->
          <el-button 
            type="primary" 
            :icon="Upload"
            @click="$router.push('/upload')"
          >
            上传
          </el-button>

          <!-- Notifications -->
          <el-badge :value="notificationCount" :hidden="notificationCount === 0">
            <el-button :icon="Bell" circle />
          </el-badge>

          <!-- User Dropdown -->
          <el-dropdown @command="handleCommand">
            <div class="flex items-center cursor-pointer">
              <img 
                :src="userStore.user?.avatar || '/default-avatar.png'" 
                :alt="userStore.user?.username"
                class="h-8 w-8 rounded-full"
              />
              <el-icon class="ml-1"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item command="analytics" v-if="userStore.isModerator">
                  <el-icon><DataAnalysis /></el-icon>
                  数据分析
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Search, 
  Upload, 
  Bell, 
  ArrowDown, 
  User, 
  Setting, 
  DataAnalysis, 
  SwitchButton 
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

const searchQuery = ref('')
const notificationCount = ref(0)

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({
      name: 'videos',
      query: { search: searchQuery.value.trim() }
    })
  }
}

const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'analytics':
      router.push('/analytics')
      break
    case 'logout':
      userStore.logout()
      router.push('/login')
      break
  }
}
</script>
