<template>
  <aside class="w-64 bg-white shadow-sm border-r h-full">
    <nav class="mt-8">
      <div class="px-4">
        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
          主要功能
        </h3>
        <ul class="space-y-1">
          <li v-for="item in mainMenuItems" :key="item.name">
            <router-link
              :to="item.path"
              class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200"
              :class="[
                $route.path === item.path
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              <el-icon class="mr-3 h-5 w-5" :class="[
                $route.path === item.path ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
              ]">
                <component :is="item.icon" />
              </el-icon>
              {{ item.name }}
            </router-link>
          </li>
        </ul>
      </div>

      <div class="px-4 mt-8">
        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
          内容管理
        </h3>
        <ul class="space-y-1">
          <li v-for="item in contentMenuItems" :key="item.name">
            <router-link
              :to="item.path"
              class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200"
              :class="[
                $route.path === item.path
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              <el-icon class="mr-3 h-5 w-5" :class="[
                $route.path === item.path ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
              ]">
                <component :is="item.icon" />
              </el-icon>
              {{ item.name }}
            </router-link>
          </li>
        </ul>
      </div>

      <div v-if="userStore.isModerator" class="px-4 mt-8">
        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
          管理功能
        </h3>
        <ul class="space-y-1">
          <li v-for="item in adminMenuItems" :key="item.name">
            <router-link
              :to="item.path"
              class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200"
              :class="[
                $route.path === item.path
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              <el-icon class="mr-3 h-5 w-5" :class="[
                $route.path === item.path ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
              ]">
                <component :is="item.icon" />
              </el-icon>
              {{ item.name }}
            </router-link>
          </li>
        </ul>
      </div>
    </nav>
  </aside>
</template>

<script setup lang="ts">
import { 
  House, 
  VideoPlay, 
  Upload, 
  List, 
  User, 
  Setting, 
  DataAnalysis,
  Management
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const mainMenuItems = [
  { name: '仪表板', path: '/dashboard', icon: House },
  { name: '视频列表', path: '/videos', icon: VideoPlay },
  { name: '上传视频', path: '/upload', icon: Upload },
  { name: '播放列表', path: '/playlists', icon: List }
]

const contentMenuItems = [
  { name: '个人资料', path: '/profile', icon: User },
  { name: '设置', path: '/settings', icon: Setting }
]

const adminMenuItems = [
  { name: '数据分析', path: '/analytics', icon: DataAnalysis },
  { name: '用户管理', path: '/admin/users', icon: Management }
]
</script>
