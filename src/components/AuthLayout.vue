<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 简化的导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo 和品牌 -->
          <div class="flex items-center">
            <router-link to="/" class="flex items-center focus:outline-none">
              <div class="h-8 w-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">WV</span>
              </div>
              <span class="ml-2 text-xl font-bold text-gray-900">WiseVideo</span>
            </router-link>
          </div>

          <!-- 语言切换 -->
          <div class="flex items-center space-x-4">
            <el-select
              :model-value="getCurrentLocale()"
              @update:model-value="setLocale"
              size="small"
              style="width: 120px"
            >
              <el-option
                v-for="locale in supportedLocales"
                :key="locale.code"
                :label="locale.flag + ' ' + locale.name"
                :value="locale.code"
              />
            </el-select>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="pt-8">
      <slot />
    </main>
  </div>
</template>

<script setup lang="ts">
import { getCurrentLocale, setLocale, supportedLocales } from '@/i18n'
</script>
