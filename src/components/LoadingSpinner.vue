<template>
  <div class="loading-container" :class="{ 'full-screen': fullScreen }">
    <el-loading 
      :text="text"
      :spinner="spinner"
      :background="background"
      :element-loading-text="text"
      v-loading="true"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  text?: string
  spinner?: string
  background?: string
  fullScreen?: boolean
}

withDefaults(defineProps<Props>(), {
  text: '加载中...',
  background: 'rgba(0, 0, 0, 0.7)',
  fullScreen: false
})
</script>

<style scoped>
.loading-container {
  position: relative;
  min-height: 200px;
}

.loading-container.full-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}
</style>
