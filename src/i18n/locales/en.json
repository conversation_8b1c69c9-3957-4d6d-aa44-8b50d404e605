{"nav": {"home": "Home", "dashboard": "Dashboard", "tools": "AI Tools", "tasks": "Tasks", "pricing": "Pricing", "admin": "Admin", "login": "Sign In", "register": "Sign Up", "logout": "Sign Out"}, "userMenu": {"profile": "Profile", "settings": "Settings", "billing": "Billing", "help": "Help & Support", "theme": "Theme", "language": "Language"}, "auth": {"validation": {"emailRequired": "Please enter your email address", "emailInvalid": "Please enter a valid email address", "passwordRequired": "Please enter your password", "passwordMinLength": "Password must be at least 6 characters", "confirmPasswordRequired": "Please confirm your password", "passwordMismatch": "Passwords do not match", "firstNameRequired": "Please enter your first name", "lastNameRequired": "Please enter your last name"}, "login": {"title": "Sign In", "subtitle": "Welcome back! Please sign in to your account.", "email": "Email Address", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "signIn": "Sign In", "noAccount": "Don't have an account?", "signUp": "Sign up here"}, "register": {"title": "Create Account", "subtitle": "Join thousands of creators using AI technology.", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "terms": "I agree to the Terms of Service and Privacy Policy", "signUp": "Sign Up", "hasAccount": "Already have an account?", "signIn": "Sign in here"}, "forgotPassword": {"title": "Forgot Password?", "subtitle": "No worries! Enter your email address and we'll send you a reset link.", "email": "Email Address", "sendLink": "Send Reset Link", "backToLogin": "Back to Sign In"}, "resetPassword": {"title": "Reset Password", "subtitle": "Please enter your new password", "password": "New Password", "confirmPassword": "Confirm New Password", "updatePassword": "Update Password", "requirements": {"title": "Password Requirements:", "length": "At least 8 characters", "uppercase": "At least one uppercase letter", "number": "At least one number", "special": "At least one special character"}}}, "home": {"hero": {"title": "Unlimited Possibilities with", "titleHighlight": "AI Creation", "subtitle": "Use cutting-edge AI technology to easily generate images, videos, audio, and 3D models. Unleash your creativity and turn imagination into reality.", "startCreating": "Start Creating for Free", "learnMore": "Learn More", "features": "✨ No Credit Card Required • 🚀 Get Started Instantly • 🎨 Unlimited Creativity"}, "features": {"title": "Powerful AI Creation Tools", "subtitle": "We provide various types of AI tools to meet all your creative needs", "imageGeneration": {"title": "Image Generation", "description": "Generate high-quality images using advanced AI models"}, "videoCreation": {"title": "Video Creation", "description": "Generate amazing video content from text descriptions"}, "audioSynthesis": {"title": "Audio Synthesis", "description": "Generate speech, music, and sound effects"}, "modeling3d": {"title": "3D Modeling", "description": "Create three-dimensional models and scenes"}}, "stats": {"title": "Numbers Speak", "subtitle": "Trusted choice of users worldwide", "activeUsers": "Active Users", "generatedWorks": "Generated Works", "uptime": "Service Availability", "support": "Technical Support"}, "cta": {"title": "Ready to Start Your AI Creation Journey?", "subtitle": "Join tens of thousands of creators using AI technology to unleash unlimited creative potential", "signUpNow": "Sign Up for Free Now", "viewPricing": "View Pricing Plans"}}, "dashboard": {"title": "Dashboard", "subtitle": "Manage your AI creation projects", "welcome": "Welcome back, {name}!", "user": "User", "welcomeMessage": "Ready to create something amazing?", "quickActions": "Quick Actions", "recentActivity": "Recent Activity", "recommendedTools": "Recommended Tools", "viewAll": "View All", "tryNow": "Try Now", "comingSoon": "Feature coming soon", "stats": {"projects": "Projects", "generated": "Generated", "timeUsed": "Time Used", "credits": "Credits Left"}, "actions": {"generateImage": "Generate Image", "createVideo": "Create Video", "generateAudio": "Generate Audio", "create3D": "Create 3D Model"}, "activity": {"imageGenerated": "Generated an image", "videoCreated": "Created a video", "audioGenerated": "Generated audio file", "timeAgo": "{time} hours ago"}, "tools": {"fluxDescription": "High-quality image generation tool", "veoDescription": "Text-to-video generation tool", "diaDescription": "High-quality speech synthesis tool"}}, "tasks": {"title": "Task Management", "subtitle": "View and manage your AI generation tasks", "createNew": "Create New Task", "progress": "Progress", "view": "View", "download": "Download", "noTasks": "No Tasks", "noTasksDesc": "You haven't created any tasks yet", "createFirst": "Create Your First Task", "createSoon": "Task creation feature coming soon", "viewSoon": "Task details feature coming soon: {title}", "downloadStart": "Starting download: {title}", "timeAgo": "{time} hours ago", "minutesAgo": "{time} minutes ago", "status": {"all": "All", "pending": "Pending", "processing": "Processing", "completed": "Completed", "failed": "Failed"}, "examples": {"imageGeneration": "AI Image Generation", "imageDesc": "Generate landscape images using Flux-Dev", "videoCreation": "AI Video Creation", "videoDesc": "Create product showcase video using Veo2-T2V", "audioGeneration": "AI Audio Generation", "audioDesc": "Generate voice narration using DIA-TTS"}}, "pricing": {"title": "Choose the Right Plan for You", "subtitle": "Start free and upgrade as your business grows", "monthly": "Monthly", "yearly": "Yearly", "save20": "Save 20%", "popular": "Most Popular", "month": "month", "year": "year", "getStarted": "Get Started", "choosePlan": "Choose <PERSON>", "freeSelected": "You have selected the Free plan", "paymentSoon": "Payment for {plan} plan coming soon", "plans": {"free": {"name": "Free", "description": "Perfect for individuals and small projects", "features": {"generations": "10 generations per month", "basicTools": "Basic AI tools", "standardQuality": "Standard quality output", "communitySupport": "Community support"}}, "pro": {"name": "Pro", "description": "Perfect for professional creators and small teams", "features": {"generations": "500 generations per month", "allTools": "All AI tools", "highQuality": "High quality output", "prioritySupport": "Priority support", "noWatermark": "No watermark"}}, "enterprise": {"name": "Enterprise", "description": "Perfect for large teams and enterprises", "features": {"unlimited": "Unlimited generations", "customModels": "Custom models", "apiAccess": "API access", "dedicatedSupport": "Dedicated support", "sla": "99.9% SLA guarantee"}}}, "faq": {"title": "Frequently Asked Questions", "q1": "Can I cancel my subscription anytime?", "a1": "Yes, you can cancel your subscription at any time. After cancellation, your account will be downgraded to the free plan at the end of the current billing cycle.", "q2": "What are generations?", "a2": "Generations refer to the number of times you can use AI tools to create content. Each use of image generation, video creation, or audio synthesis consumes one generation.", "q3": "Can I upgrade or downgrade my plan?", "a3": "Absolutely. You can upgrade to a higher plan at any time, or downgrade to a lower plan for the next billing cycle.", "q4": "What additional features does Enterprise include?", "a4": "Enterprise includes unlimited generations, custom AI models, API access, dedicated account manager, and 99.9% service level agreement guarantee."}}, "admin": {"title": "<PERSON><PERSON>", "subtitle": "System administration and monitoring", "userManagement": "User Management", "totalUsers": "Total Users", "activeUsers": "Active Users", "subscriptions": "Subscription Management", "activeSubscriptions": "Active Subscriptions", "manage": "Manage", "systemMonitoring": "System Monitoring", "serverStatus": "Server Status", "online": "Online", "apiHealth": "API Health", "healthy": "Healthy", "queueStatus": "Queue Status", "busy": "Busy", "cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "contentManagement": "Content Management", "manageImages": "Manage Images", "manageVideos": "Manage Videos", "manageAudio": "Manage Audio", "manageModels": "Manage Models", "systemSettings": "System Settings", "generalSettings": "General Settings", "securitySettings": "Security Settings", "backupRestore": "Backup & Restore", "systemLogs": "System Logs", "userManagementSoon": "User management feature coming soon", "subscriptionManagementSoon": "Subscription management feature coming soon", "contentManagementSoon": "{type} content management feature coming soon", "systemSettingsSoon": "{type} system settings feature coming soon", "stats": {"totalUsers": "Total Users", "totalGenerations": "Total Generations", "revenue": "Revenue", "avgProcessTime": "Avg Process Time"}}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "notifications": "Notifications"}, "theme": {"light": "Light", "dark": "Dark", "auto": "Auto"}, "notifications": {"markAllRead": "<PERSON> as <PERSON>", "noNotifications": "No notifications"}}