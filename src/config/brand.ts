/**
 * 品牌配置文件
 * 全局存储产品名称、Logo、颜色等品牌相关信息
 */

export const BRAND_CONFIG = {
  // 产品名称
  name: 'WiseVideo',
  
  // 产品描述
  description: {
    zh: '智能视频创作平台',
    en: 'AI-Powered Video Creation Platform'
  },
  
  // 产品标语
  tagline: {
    zh: '用 AI 重新定义视频创作',
    en: 'Redefining Video Creation with AI'
  },
  
  // Logo 相关
  logo: {
    // Logo 文件路径
    main: '/logo/wisevideo-logo.svg',
    icon: '/logo/wisevideo-icon.svg',
    horizontal: '/logo/wisevideo-horizontal.svg',
    
    // Logo 尺寸规范
    sizes: {
      sm: { width: 120, height: 36 },
      md: { width: 200, height: 60 },
      lg: { width: 300, height: 90 }
    }
  },
  
  // 品牌色彩
  colors: {
    primary: {
      gradient: 'from-indigo-500 to-purple-500',
      solid: '#6366f1'
    },
    accent: '#06b6d4',
    text: {
      primary: '#1a1a1a',
      secondary: '#6b7280'
    }
  },
  
  // 字体规范
  typography: {
    primary: 'Inter, system-ui, -apple-system, sans-serif',
    weights: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700
    }
  },
  
  // 社交媒体
  social: {
    website: 'https://wisevideo.ai',
    twitter: '@wisevideo',
    github: 'wisevideo'
  },
  
  // 公司信息
  company: {
    name: 'WiseVideo Inc.',
    founded: '2024',
    location: 'San Francisco, CA'
  }
} as const

// 导出常用的品牌信息
export const PRODUCT_NAME = BRAND_CONFIG.name
export const PRODUCT_DESCRIPTION = BRAND_CONFIG.description
export const PRODUCT_TAGLINE = BRAND_CONFIG.tagline
export const BRAND_COLORS = BRAND_CONFIG.colors
export const LOGO_CONFIG = BRAND_CONFIG.logo
