<template>
  <AppLayout>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">{{ t('admin.title') }}</h1>
        <p class="mt-2 text-gray-600">{{ t('admin.subtitle') }}</p>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div
          v-for="stat in adminStats"
          :key="stat.title"
          class="bg-white rounded-lg shadow p-6"
        >
          <div class="flex items-center">
            <div :class="[stat.iconBg, 'p-3 rounded-lg']">
              <el-icon :size="24" :class="stat.iconColor">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ stat.title }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ stat.value }}</p>
              <p :class="[
                'text-sm',
                stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
              ]">
                {{ stat.change }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 管理功能 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- 用户管理 -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ t('admin.userManagement') }}</h3>
          <div class="space-y-4">
            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div class="flex items-center">
                <el-icon class="text-blue-600 mr-3" size="20">
                  <User />
                </el-icon>
                <div>
                  <p class="font-medium text-gray-900">{{ t('admin.totalUsers') }}</p>
                  <p class="text-sm text-gray-500">{{ t('admin.activeUsers') }}</p>
                </div>
              </div>
              <el-button size="small" @click="handleManageUsers">
                {{ t('admin.manage') }}
              </el-button>
            </div>
            
            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div class="flex items-center">
                <el-icon class="text-green-600 mr-3" size="20">
                  <CreditCard />
                </el-icon>
                <div>
                  <p class="font-medium text-gray-900">{{ t('admin.subscriptions') }}</p>
                  <p class="text-sm text-gray-500">{{ t('admin.activeSubscriptions') }}</p>
                </div>
              </div>
              <el-button size="small" @click="handleManageSubscriptions">
                {{ t('admin.manage') }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 系统监控 -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ t('admin.systemMonitoring') }}</h3>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-gray-700">{{ t('admin.serverStatus') }}</span>
              <el-tag type="success">{{ t('admin.online') }}</el-tag>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-gray-700">{{ t('admin.apiHealth') }}</span>
              <el-tag type="success">{{ t('admin.healthy') }}</el-tag>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-gray-700">{{ t('admin.queueStatus') }}</span>
              <el-tag type="warning">{{ t('admin.busy') }}</el-tag>
            </div>
            
            <div>
              <div class="flex justify-between text-sm text-gray-600 mb-1">
                <span>{{ t('admin.cpuUsage') }}</span>
                <span>65%</span>
              </div>
              <el-progress :percentage="65" :show-text="false" />
            </div>
            
            <div>
              <div class="flex justify-between text-sm text-gray-600 mb-1">
                <span>{{ t('admin.memoryUsage') }}</span>
                <span>42%</span>
              </div>
              <el-progress :percentage="42" :show-text="false" />
            </div>
          </div>
        </div>

        <!-- 内容管理 -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ t('admin.contentManagement') }}</h3>
          <div class="grid grid-cols-2 gap-4">
            <el-button class="h-20 flex flex-col items-center justify-center" @click="handleManageContent('images')">
              <el-icon size="24" class="text-pink-600 mb-2">
                <PictureRounded />
              </el-icon>
              <span class="text-sm">{{ t('admin.manageImages') }}</span>
            </el-button>
            
            <el-button class="h-20 flex flex-col items-center justify-center" @click="handleManageContent('videos')">
              <el-icon size="24" class="text-blue-600 mb-2">
                <VideoPlay />
              </el-icon>
              <span class="text-sm">{{ t('admin.manageVideos') }}</span>
            </el-button>
            
            <el-button class="h-20 flex flex-col items-center justify-center" @click="handleManageContent('audio')">
              <el-icon size="24" class="text-green-600 mb-2">
                <Headphone />
              </el-icon>
              <span class="text-sm">{{ t('admin.manageAudio') }}</span>
            </el-button>
            
            <el-button class="h-20 flex flex-col items-center justify-center" @click="handleManageContent('models')">
              <el-icon size="24" class="text-purple-600 mb-2">
                <Box />
              </el-icon>
              <span class="text-sm">{{ t('admin.manageModels') }}</span>
            </el-button>
          </div>
        </div>

        <!-- 系统设置 -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ t('admin.systemSettings') }}</h3>
          <div class="space-y-4">
            <el-button class="w-full justify-start" @click="handleSystemSettings('general')">
              <el-icon class="mr-2">
                <Setting />
              </el-icon>
              {{ t('admin.generalSettings') }}
            </el-button>
            
            <el-button class="w-full justify-start" @click="handleSystemSettings('security')">
              <el-icon class="mr-2">
                <Lock />
              </el-icon>
              {{ t('admin.securitySettings') }}
            </el-button>
            
            <el-button class="w-full justify-start" @click="handleSystemSettings('backup')">
              <el-icon class="mr-2">
                <Download />
              </el-icon>
              {{ t('admin.backupRestore') }}
            </el-button>
            
            <el-button class="w-full justify-start" @click="handleSystemSettings('logs')">
              <el-icon class="mr-2">
                <Document />
              </el-icon>
              {{ t('admin.systemLogs') }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import {
  User,
  CreditCard,
  TrendCharts,
  Clock,
  PictureRounded,
  VideoPlay,
  Headphone,
  Box,
  Setting,
  Lock,
  Download,
  Document
} from '@element-plus/icons-vue'
import AppLayout from '@/components/AppLayout.vue'

const { t } = useI18n()

// 管理员统计数据
const adminStats = computed(() => [
  {
    title: t('admin.stats.totalUsers'),
    value: '2,847',
    change: '+12%',
    icon: User,
    iconColor: 'text-blue-600',
    iconBg: 'bg-blue-100'
  },
  {
    title: t('admin.stats.totalGenerations'),
    value: '18,392',
    change: '+8%',
    icon: TrendCharts,
    iconColor: 'text-green-600',
    iconBg: 'bg-green-100'
  },
  {
    title: t('admin.stats.revenue'),
    value: '$12,847',
    change: '+15%',
    icon: CreditCard,
    iconColor: 'text-purple-600',
    iconBg: 'bg-purple-100'
  },
  {
    title: t('admin.stats.avgProcessTime'),
    value: '2.3s',
    change: '-5%',
    icon: Clock,
    iconColor: 'text-yellow-600',
    iconBg: 'bg-yellow-100'
  }
])

// 处理用户管理
const handleManageUsers = () => {
  ElMessage.info(t('admin.userManagementSoon'))
}

// 处理订阅管理
const handleManageSubscriptions = () => {
  ElMessage.info(t('admin.subscriptionManagementSoon'))
}

// 处理内容管理
const handleManageContent = (type: string) => {
  ElMessage.info(t('admin.contentManagementSoon', { type }))
}

// 处理系统设置
const handleSystemSettings = (type: string) => {
  ElMessage.info(t('admin.systemSettingsSoon', { type }))
}
</script>
