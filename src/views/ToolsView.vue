<template>
  <AppLayout>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">{{ t('tools.title') }}</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">{{ t('tools.subtitle') }}</p>
      </div>

      <!-- 工具分类 -->
      <div class="mb-8">
        <div class="flex flex-wrap justify-center gap-4">
          <button
            v-for="category in categories"
            :key="category.id"
            :class="[
              'px-6 py-2 rounded-full text-sm font-medium transition-colors duration-200',
              selectedCategory === category.id
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            ]"
            @click="selectedCategory = category.id"
          >
            {{ category.name }}
          </button>
        </div>
      </div>

      <!-- 工具网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div
          v-for="tool in filteredTools"
          :key="tool.id"
          class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
        >
          <!-- 工具图标和标题 -->
          <div class="p-6">
            <div class="flex items-center mb-4">
              <div :class="[tool.iconBg, 'p-3 rounded-lg']">
                <el-icon :size="32" :class="tool.iconColor">
                  <component :is="tool.icon" />
                </el-icon>
              </div>
              <div class="ml-4">
                <h3 class="text-lg font-semibold text-gray-900">{{ tool.name }}</h3>
                <p class="text-sm text-gray-500">{{ tool.category }}</p>
              </div>
            </div>
            
            <p class="text-gray-600 mb-4">{{ tool.description }}</p>
            
            <!-- 特性标签 -->
            <div class="flex flex-wrap gap-2 mb-4">
              <span
                v-for="feature in tool.features"
                :key="feature"
                class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
              >
                {{ feature }}
              </span>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex space-x-2">
              <el-button type="primary" class="flex-1" @click="handleTryTool(tool)">
                {{ t('tools.tryNow') }}
              </el-button>
              <el-button @click="handleLearnMore(tool)">
                {{ t('tools.learnMore') }}
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredTools.length === 0" class="text-center py-12">
        <el-icon size="64" class="text-gray-400 mb-4">
          <Box />
        </el-icon>
        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ t('tools.noTools') }}</h3>
        <p class="text-gray-500">{{ t('tools.noToolsDesc') }}</p>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import {
  PictureRounded,
  VideoPlay,
  Headset,
  Box,
  MagicStick
} from '@element-plus/icons-vue'
import AppLayout from '@/components/AppLayout.vue'

const { t } = useI18n()

const selectedCategory = ref('all')

// 工具分类
const categories = computed(() => [
  { id: 'all', name: t('tools.categories.all') },
  { id: 'image', name: t('tools.categories.image') },
  { id: 'video', name: t('tools.categories.video') },
  { id: 'audio', name: t('tools.categories.audio') },
  { id: '3d', name: t('tools.categories.3d') }
])

// 工具列表
const tools = computed(() => [
  {
    id: 1,
    name: 'Flux-Dev',
    category: t('tools.categories.image'),
    categoryId: 'image',
    description: t('tools.flux.description'),
    features: [t('tools.features.highQuality'), t('tools.features.fast'), t('tools.features.flexible')],
    icon: PictureRounded,
    iconColor: 'text-pink-600',
    iconBg: 'bg-pink-100'
  },
  {
    id: 2,
    name: 'SDXL',
    category: t('tools.categories.image'),
    categoryId: 'image',
    description: t('tools.sdxl.description'),
    features: [t('tools.features.stable'), t('tools.features.detailed'), t('tools.features.customizable')],
    icon: PictureRounded,
    iconColor: 'text-purple-600',
    iconBg: 'bg-purple-100'
  },
  {
    id: 3,
    name: 'Veo2-T2V',
    category: t('tools.categories.video'),
    categoryId: 'video',
    description: t('tools.veo.description'),
    features: [t('tools.features.textToVideo'), t('tools.features.realistic'), t('tools.features.longForm')],
    icon: VideoPlay,
    iconColor: 'text-blue-600',
    iconBg: 'bg-blue-100'
  },
  {
    id: 4,
    name: 'DIA-TTS',
    category: t('tools.categories.audio'),
    categoryId: 'audio',
    description: t('tools.dia.description'),
    features: [t('tools.features.naturalVoice'), t('tools.features.multiLanguage'), t('tools.features.emotional')],
    icon: Headset,
    iconColor: 'text-green-600',
    iconBg: 'bg-green-100'
  },
  {
    id: 5,
    name: 'Hunyuan3D-v2',
    category: t('tools.categories.3d'),
    categoryId: '3d',
    description: t('tools.hunyuan.description'),
    features: [t('tools.features.3dModeling'), t('tools.features.textTo3d'), t('tools.features.highDetail')],
    icon: Box,
    iconColor: 'text-orange-600',
    iconBg: 'bg-orange-100'
  }
])

// 过滤工具
const filteredTools = computed(() => {
  if (selectedCategory.value === 'all') {
    return tools.value
  }
  return tools.value.filter(tool => tool.categoryId === selectedCategory.value)
})

// 处理尝试工具
const handleTryTool = (tool: any) => {
  ElMessage.info(t('tools.comingSoon', { name: tool.name }))
  // TODO: 实现具体的工具使用逻辑
}

// 处理了解更多
const handleLearnMore = (tool: any) => {
  ElMessage.info(t('tools.moreInfoSoon', { name: tool.name }))
  // TODO: 实现工具详情页面
}
</script>
