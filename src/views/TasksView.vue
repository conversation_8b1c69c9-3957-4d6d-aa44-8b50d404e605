<template>
  <AppLayout>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题 -->
      <div class="flex justify-between items-center mb-8">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">{{ t('tasks.title') }}</h1>
          <p class="mt-2 text-gray-600">{{ t('tasks.subtitle') }}</p>
        </div>
        <el-button type="primary" @click="handleCreateTask">
          {{ t('tasks.createNew') }}
        </el-button>
      </div>

      <!-- 任务状态筛选 -->
      <div class="mb-6">
        <div class="flex flex-wrap gap-2">
          <button
            v-for="status in taskStatuses"
            :key="status.id"
            :class="[
              'px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200',
              selectedStatus === status.id
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            ]"
            @click="selectedStatus = status.id"
          >
            {{ status.name }}
            <span class="ml-2 px-2 py-1 bg-white/20 rounded-full text-xs">
              {{ getTaskCountByStatus(status.id) }}
            </span>
          </button>
        </div>
      </div>

      <!-- 任务列表 -->
      <div class="space-y-4">
        <div
          v-for="task in filteredTasks"
          :key="task.id"
          class="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow duration-200"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center mb-2">
                <div :class="[task.iconBg, 'p-2 rounded-lg mr-3']">
                  <el-icon :size="20" :class="task.iconColor">
                    <component :is="task.icon" />
                  </el-icon>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">{{ task.title }}</h3>
                  <p class="text-sm text-gray-500">{{ task.type }} • {{ task.createdAt }}</p>
                </div>
              </div>
              
              <p class="text-gray-600 mb-3">{{ task.description }}</p>
              
              <div class="flex items-center space-x-4">
                <span :class="[
                  'px-2 py-1 rounded-full text-xs font-medium',
                  getStatusColor(task.status)
                ]">
                  {{ getStatusText(task.status) }}
                </span>
                <span class="text-sm text-gray-500">
                  {{ t('tasks.progress') }}: {{ task.progress }}%
                </span>
              </div>
            </div>
            
            <div class="flex space-x-2 ml-4">
              <el-button size="small" @click="handleViewTask(task)">
                {{ t('tasks.view') }}
              </el-button>
              <el-button 
                v-if="task.status === 'completed'"
                type="primary" 
                size="small" 
                @click="handleDownload(task)"
              >
                {{ t('tasks.download') }}
              </el-button>
            </div>
          </div>
          
          <!-- 进度条 -->
          <div class="mt-4">
            <el-progress 
              :percentage="task.progress" 
              :status="task.status === 'failed' ? 'exception' : task.status === 'completed' ? 'success' : undefined"
              :show-text="false"
            />
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredTasks.length === 0" class="text-center py-12">
        <el-icon size="64" class="text-gray-400 mb-4">
          <Document />
        </el-icon>
        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ t('tasks.noTasks') }}</h3>
        <p class="text-gray-500 mb-4">{{ t('tasks.noTasksDesc') }}</p>
        <el-button type="primary" @click="handleCreateTask">
          {{ t('tasks.createFirst') }}
        </el-button>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import {
  PictureRounded,
  VideoPlay,
  Headset,
  Box,
  Document
} from '@element-plus/icons-vue'
import AppLayout from '@/components/AppLayout.vue'

const { t } = useI18n()

const selectedStatus = ref('all')

// 任务状态
const taskStatuses = computed(() => [
  { id: 'all', name: t('tasks.status.all') },
  { id: 'pending', name: t('tasks.status.pending') },
  { id: 'processing', name: t('tasks.status.processing') },
  { id: 'completed', name: t('tasks.status.completed') },
  { id: 'failed', name: t('tasks.status.failed') }
])

// 模拟任务数据
const tasks = computed(() => [
  {
    id: 1,
    title: t('tasks.examples.imageGeneration'),
    description: t('tasks.examples.imageDesc'),
    type: t('tools.categories.image'),
    status: 'completed',
    progress: 100,
    createdAt: t('tasks.timeAgo', { time: '2' }),
    icon: PictureRounded,
    iconColor: 'text-pink-600',
    iconBg: 'bg-pink-100'
  },
  {
    id: 2,
    title: t('tasks.examples.videoCreation'),
    description: t('tasks.examples.videoDesc'),
    type: t('tools.categories.video'),
    status: 'processing',
    progress: 65,
    createdAt: t('tasks.timeAgo', { time: '1' }),
    icon: VideoPlay,
    iconColor: 'text-blue-600',
    iconBg: 'bg-blue-100'
  },
  {
    id: 3,
    title: t('tasks.examples.audioGeneration'),
    description: t('tasks.examples.audioDesc'),
    type: t('tools.categories.audio'),
    status: 'pending',
    progress: 0,
    createdAt: t('tasks.timeAgo', { time: '30' }),
    icon: Headset,
    iconColor: 'text-green-600',
    iconBg: 'bg-green-100'
  }
])

// 过滤任务
const filteredTasks = computed(() => {
  if (selectedStatus.value === 'all') {
    return tasks.value
  }
  return tasks.value.filter(task => task.status === selectedStatus.value)
})

// 获取状态对应的任务数量
const getTaskCountByStatus = (status: string) => {
  if (status === 'all') {
    return tasks.value.length
  }
  return tasks.value.filter(task => task.status === status).length
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

// 获取状态文本
const getStatusText = (status: string) => {
  return t(`tasks.status.${status}`)
}

// 处理创建任务
const handleCreateTask = () => {
  ElMessage.info(t('tasks.createSoon'))
}

// 处理查看任务
const handleViewTask = (task: any) => {
  ElMessage.info(t('tasks.viewSoon', { title: task.title }))
}

// 处理下载
const handleDownload = (task: any) => {
  ElMessage.success(t('tasks.downloadStart', { title: task.title }))
}
</script>
