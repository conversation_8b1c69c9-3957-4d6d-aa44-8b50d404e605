<template>
  <AppLayout>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">{{ t('pricing.title') }}</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">{{ t('pricing.subtitle') }}</p>
      </div>

      <!-- 计费周期切换 -->
      <div class="flex justify-center mb-12">
        <div class="bg-gray-100 p-1 rounded-lg">
          <button
            :class="[
              'px-6 py-2 rounded-md text-sm font-medium transition-colors duration-200',
              billingCycle === 'monthly'
                ? 'bg-white text-gray-900 shadow'
                : 'text-gray-600 hover:text-gray-900'
            ]"
            @click="billingCycle = 'monthly'"
          >
            {{ t('pricing.monthly') }}
          </button>
          <button
            :class="[
              'px-6 py-2 rounded-md text-sm font-medium transition-colors duration-200',
              billingCycle === 'yearly'
                ? 'bg-white text-gray-900 shadow'
                : 'text-gray-600 hover:text-gray-900'
            ]"
            @click="billingCycle = 'yearly'"
          >
            {{ t('pricing.yearly') }}
            <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
              {{ t('pricing.save20') }}
            </span>
          </button>
        </div>
      </div>

      <!-- 价格卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12 mt-12">
        <div
          v-for="plan in plans"
          :key="plan.id"
          :class="[
            'bg-white rounded-lg shadow-lg relative',
            plan.popular ? 'ring-2 ring-blue-600 shadow-xl' : 'shadow-md'
          ]"
        >
          <!-- 热门标签 -->
          <div v-if="plan.popular" class="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
            <span class="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium shadow-lg">
              {{ t('pricing.popular') }}
            </span>
          </div>

          <div class="p-8">
            <!-- 计划名称和价格 -->
            <div class="text-center mb-6">
              <h3 class="text-2xl font-bold text-gray-900 mb-3">{{ plan.name }}</h3>
              <div class="mb-4">
                <span class="text-5xl font-bold text-gray-900">
                  ${{ billingCycle === 'monthly' ? plan.monthlyPrice : plan.yearlyPrice }}
                </span>
                <span class="text-lg text-gray-600 ml-1">
                  /{{ billingCycle === 'monthly' ? t('pricing.month') : t('pricing.year') }}
                </span>
              </div>
              <p class="text-gray-600 text-sm">{{ plan.description }}</p>
            </div>

            <!-- 功能列表 -->
            <ul class="space-y-3 mb-8">
              <li
                v-for="feature in plan.features"
                :key="feature"
                class="flex items-center"
              >
                <el-icon class="text-green-600 mr-3" size="16">
                  <Check />
                </el-icon>
                <span class="text-gray-700">{{ feature }}</span>
              </li>
            </ul>

            <!-- 选择按钮 -->
            <el-button
              :class="[
                'w-full font-medium',
                plan.popular
                  ? 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600'
                  : plan.id === 'enterprise'
                    ? 'bg-gray-800 hover:bg-gray-900 text-white border-gray-800'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border-gray-300'
              ]"
              size="large"
              @click="handleSelectPlan(plan)"
            >
              {{ getButtonText(plan) }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 常见问题 -->
      <div class="max-w-3xl mx-auto">
        <h2 class="text-2xl font-bold text-gray-900 text-center mb-8">{{ t('pricing.faq.title') }}</h2>
        <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div
            v-for="(faq, index) in faqs"
            :key="faq.id"
            :class="[
              'border-gray-200',
              index < faqs.length - 1 ? 'border-b' : ''
            ]"
          >
            <button
              @click="toggleFaq(faq.id)"
              class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
            >
              <span class="text-lg font-medium text-gray-900">{{ faq.question }}</span>
              <el-icon
                :class="[
                  'text-gray-400 transition-transform duration-200',
                  openFaqs.includes(faq.id) ? 'rotate-180' : ''
                ]"
              >
                <ArrowDown />
              </el-icon>
            </button>
            <div
              v-if="openFaqs.includes(faq.id)"
              class="px-6 pb-4 text-gray-600 leading-relaxed"
            >
              {{ faq.answer }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { Check, ArrowDown } from '@element-plus/icons-vue'
import AppLayout from '@/components/AppLayout.vue'

const { t } = useI18n()

const billingCycle = ref<'monthly' | 'yearly'>('monthly')

// FAQ 展开状态
const openFaqs = ref<string[]>([])

// 切换 FAQ 展开状态
const toggleFaq = (faqId: string) => {
  const index = openFaqs.value.indexOf(faqId)
  if (index > -1) {
    openFaqs.value.splice(index, 1)
  } else {
    openFaqs.value.push(faqId)
  }
}

// 价格计划
const plans = computed(() => [
  {
    id: 'free',
    name: t('pricing.plans.free.name'),
    description: t('pricing.plans.free.description'),
    monthlyPrice: 0,
    yearlyPrice: 0,
    popular: false,
    features: [
      t('pricing.plans.free.features.generations'),
      t('pricing.plans.free.features.basicTools'),
      t('pricing.plans.free.features.standardQuality'),
      t('pricing.plans.free.features.communitySupport')
    ]
  },
  {
    id: 'pro',
    name: t('pricing.plans.pro.name'),
    description: t('pricing.plans.pro.description'),
    monthlyPrice: 29,
    yearlyPrice: 23,
    popular: true,
    features: [
      t('pricing.plans.pro.features.generations'),
      t('pricing.plans.pro.features.allTools'),
      t('pricing.plans.pro.features.highQuality'),
      t('pricing.plans.pro.features.prioritySupport'),
      t('pricing.plans.pro.features.noWatermark')
    ]
  },
  {
    id: 'enterprise',
    name: t('pricing.plans.enterprise.name'),
    description: t('pricing.plans.enterprise.description'),
    monthlyPrice: 99,
    yearlyPrice: 79,
    popular: false,
    features: [
      t('pricing.plans.enterprise.features.unlimited'),
      t('pricing.plans.enterprise.features.customModels'),
      t('pricing.plans.enterprise.features.apiAccess'),
      t('pricing.plans.enterprise.features.dedicatedSupport'),
      t('pricing.plans.enterprise.features.sla')
    ]
  }
])

// 常见问题
const faqs = computed(() => [
  {
    id: '1',
    question: t('pricing.faq.q1'),
    answer: t('pricing.faq.a1')
  },
  {
    id: '2',
    question: t('pricing.faq.q2'),
    answer: t('pricing.faq.a2')
  },
  {
    id: '3',
    question: t('pricing.faq.q3'),
    answer: t('pricing.faq.a3')
  },
  {
    id: '4',
    question: t('pricing.faq.q4'),
    answer: t('pricing.faq.a4')
  }
])

// 获取按钮文本
const getButtonText = (plan: any) => {
  if (plan.id === 'free') {
    return t('pricing.getStarted')
  } else if (plan.id === 'enterprise') {
    return 'Contact Sales'
  } else {
    return 'Start Free Trial'
  }
}

// 处理选择计划
const handleSelectPlan = (plan: any) => {
  if (plan.id === 'free') {
    ElMessage.success(t('pricing.freeSelected'))
  } else {
    ElMessage.info(t('pricing.paymentSoon', { plan: plan.name }))
  }
}
</script>
