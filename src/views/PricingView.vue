<template>
  <AppLayout>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">{{ t('pricing.title') }}</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">{{ t('pricing.subtitle') }}</p>
      </div>

      <!-- 计费周期切换 -->
      <div class="flex justify-center mb-12">
        <div class="bg-gray-100 p-1 rounded-lg">
          <button
            :class="[
              'px-6 py-2 rounded-md text-sm font-medium transition-colors duration-200',
              billingCycle === 'monthly'
                ? 'bg-white text-gray-900 shadow'
                : 'text-gray-600 hover:text-gray-900'
            ]"
            @click="billingCycle = 'monthly'"
          >
            {{ t('pricing.monthly') }}
          </button>
          <button
            :class="[
              'px-6 py-2 rounded-md text-sm font-medium transition-colors duration-200',
              billingCycle === 'yearly'
                ? 'bg-white text-gray-900 shadow'
                : 'text-gray-600 hover:text-gray-900'
            ]"
            @click="billingCycle = 'yearly'"
          >
            {{ t('pricing.yearly') }}
            <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
              {{ t('pricing.save20') }}
            </span>
          </button>
        </div>
      </div>

      <!-- 价格卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12 mt-12">
        <div
          v-for="plan in plans"
          :key="plan.id"
          :class="[
            'bg-white rounded-lg shadow-lg overflow-hidden relative',
            plan.popular ? 'ring-2 ring-blue-500' : ''
          ]"
        >
          <!-- 热门标签 -->
          <div v-if="plan.popular" class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium shadow-lg">
              {{ t('pricing.popular') }}
            </span>
          </div>

          <div class="p-8">
            <!-- 计划名称和价格 -->
            <div class="text-center mb-6">
              <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ plan.name }}</h3>
              <div class="mb-4">
                <span class="text-4xl font-bold text-gray-900">
                  ${{ billingCycle === 'monthly' ? plan.monthlyPrice : plan.yearlyPrice }}
                </span>
                <span class="text-gray-600">
                  /{{ billingCycle === 'monthly' ? t('pricing.month') : t('pricing.year') }}
                </span>
              </div>
              <p class="text-gray-600">{{ plan.description }}</p>
            </div>

            <!-- 功能列表 -->
            <ul class="space-y-3 mb-8">
              <li
                v-for="feature in plan.features"
                :key="feature"
                class="flex items-center"
              >
                <el-icon class="text-green-500 mr-3" size="16">
                  <Check />
                </el-icon>
                <span class="text-gray-700">{{ feature }}</span>
              </li>
            </ul>

            <!-- 选择按钮 -->
            <el-button
              :type="plan.popular ? 'primary' : 'default'"
              class="w-full"
              size="large"
              @click="handleSelectPlan(plan)"
            >
              {{ plan.id === 'free' ? t('pricing.getStarted') : t('pricing.choosePlan') }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 常见问题 -->
      <div class="max-w-3xl mx-auto">
        <h2 class="text-2xl font-bold text-gray-900 text-center mb-8">{{ t('pricing.faq.title') }}</h2>
        <el-collapse>
          <el-collapse-item
            v-for="faq in faqs"
            :key="faq.id"
            :title="faq.question"
            :name="faq.id"
          >
            <p class="text-gray-600">{{ faq.answer }}</p>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
import AppLayout from '@/components/AppLayout.vue'

const { t } = useI18n()

const billingCycle = ref<'monthly' | 'yearly'>('monthly')

// 价格计划
const plans = computed(() => [
  {
    id: 'free',
    name: t('pricing.plans.free.name'),
    description: t('pricing.plans.free.description'),
    monthlyPrice: 0,
    yearlyPrice: 0,
    popular: false,
    features: [
      t('pricing.plans.free.features.generations'),
      t('pricing.plans.free.features.basicTools'),
      t('pricing.plans.free.features.standardQuality'),
      t('pricing.plans.free.features.communitySupport')
    ]
  },
  {
    id: 'pro',
    name: t('pricing.plans.pro.name'),
    description: t('pricing.plans.pro.description'),
    monthlyPrice: 29,
    yearlyPrice: 23,
    popular: true,
    features: [
      t('pricing.plans.pro.features.generations'),
      t('pricing.plans.pro.features.allTools'),
      t('pricing.plans.pro.features.highQuality'),
      t('pricing.plans.pro.features.prioritySupport'),
      t('pricing.plans.pro.features.noWatermark')
    ]
  },
  {
    id: 'enterprise',
    name: t('pricing.plans.enterprise.name'),
    description: t('pricing.plans.enterprise.description'),
    monthlyPrice: 99,
    yearlyPrice: 79,
    popular: false,
    features: [
      t('pricing.plans.enterprise.features.unlimited'),
      t('pricing.plans.enterprise.features.customModels'),
      t('pricing.plans.enterprise.features.apiAccess'),
      t('pricing.plans.enterprise.features.dedicatedSupport'),
      t('pricing.plans.enterprise.features.sla')
    ]
  }
])

// 常见问题
const faqs = computed(() => [
  {
    id: '1',
    question: t('pricing.faq.q1'),
    answer: t('pricing.faq.a1')
  },
  {
    id: '2',
    question: t('pricing.faq.q2'),
    answer: t('pricing.faq.a2')
  },
  {
    id: '3',
    question: t('pricing.faq.q3'),
    answer: t('pricing.faq.a3')
  },
  {
    id: '4',
    question: t('pricing.faq.q4'),
    answer: t('pricing.faq.a4')
  }
])

// 处理选择计划
const handleSelectPlan = (plan: any) => {
  if (plan.id === 'free') {
    ElMessage.success(t('pricing.freeSelected'))
  } else {
    ElMessage.info(t('pricing.paymentSoon', { plan: plan.name }))
  }
}
</script>
