<template>
  <AuthLayout>
    <div class="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <div>
          <div class="mx-auto h-12 w-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <i class="fas fa-key text-2xl text-white"></i>
          </div>
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">{{ t('auth.forgotPassword.title') }}</h2>
          <p class="mt-2 text-center text-sm text-gray-600">
            {{ t('auth.forgotPassword.subtitle') }}
          </p>
        </div>

      <el-form
        ref="forgotFormRef"
        :model="forgotForm"
        :rules="forgotRules"
        @submit.prevent="handleForgotPassword"
        class="space-y-6"
      >
        <el-form-item prop="email">
          <el-input
            v-model="forgotForm.email"
            type="email"
            placeholder="邮箱地址"
            size="large"
            :prefix-icon="Message"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="w-full"
            :loading="userStore.loading"
            @click="handleForgotPassword"
          >
            <i class="fas fa-paper-plane mr-2"></i>
            发送重置链接
          </el-button>
        </el-form-item>

        <div class="text-center">
          <router-link to="/login" class="font-medium text-blue-600 hover:text-blue-500">
            <i class="fas fa-arrow-left mr-2"></i>返回登录
          </router-link>
        </div>
      </el-form>
    </div>
  </AuthLayout>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Message } from '@element-plus/icons-vue'
import AuthLayout from '@/components/AuthLayout.vue'
import { useUserStore } from '@/stores/user'

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()

const forgotFormRef = ref()

const forgotForm = reactive({
  email: ''
})

const forgotRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
}

const handleForgotPassword = async () => {
  if (!forgotFormRef.value) return

  try {
    await forgotFormRef.value.validate()
    
    await userStore.forgotPassword({
      email: forgotForm.email
    })
    
    // 显示成功消息后跳转到登录页
    setTimeout(() => {
      router.push('/login')
    }, 2000)
    
  } catch (error) {
    console.error('Forgot password error:', error)
  }
}
</script>
