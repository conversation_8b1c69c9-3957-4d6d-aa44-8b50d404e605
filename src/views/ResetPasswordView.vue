<template>
  <AuthLayout>
    <div class="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <div>
          <div class="mx-auto h-12 w-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <i class="fas fa-lock text-2xl text-white"></i>
          </div>
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">{{ t('auth.resetPassword.title') }}</h2>
          <p class="mt-2 text-center text-sm text-gray-600">
            {{ t('auth.resetPassword.subtitle') }}
          </p>
        </div>

      <el-form
        ref="resetFormRef"
        :model="resetForm"
        :rules="resetRules"
        @submit.prevent="handleResetPassword"
        class="space-y-6"
      >
        <el-form-item prop="password">
          <el-input
            v-model="resetForm.password"
            type="password"
            placeholder="新密码"
            size="large"
            :prefix-icon="Lock"
            show-password
          />
        </el-form-item>

        <el-form-item prop="confirmPassword">
          <el-input
            v-model="resetForm.confirmPassword"
            type="password"
            placeholder="确认新密码"
            size="large"
            :prefix-icon="Lock"
            show-password
          />
        </el-form-item>

        <!-- 密码要求 -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <h4 class="text-sm font-medium text-gray-700 mb-2">密码要求：</h4>
          <ul class="text-sm text-gray-600 space-y-1">
            <li class="flex items-center">
              <i class="fas fa-check text-green-500 mr-2"></i>
              至少 8 个字符
            </li>
            <li class="flex items-center">
              <i class="fas fa-check text-green-500 mr-2"></i>
              至少一个大写字母
            </li>
            <li class="flex items-center">
              <i class="fas fa-check text-green-500 mr-2"></i>
              至少一个数字
            </li>
            <li class="flex items-center">
              <i class="fas fa-check text-green-500 mr-2"></i>
              至少一个特殊字符
            </li>
          </ul>
        </div>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="w-full"
            :loading="userStore.loading"
            @click="handleResetPassword"
          >
            <i class="fas fa-check mr-2"></i>
            更新密码
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </AuthLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Lock } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import AuthLayout from '@/components/AuthLayout.vue'
import { useUserStore } from '@/stores/user'

const { t } = useI18n()
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const resetFormRef = ref()

const resetForm = reactive({
  password: '',
  confirmPassword: ''
})

const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== resetForm.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const resetRules = {
  password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能少于8位', trigger: 'blur' },
    { 
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      message: '密码必须包含大小写字母、数字和特殊字符', 
      trigger: 'blur' 
    }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

const handleResetPassword = async () => {
  if (!resetFormRef.value) return

  try {
    await resetFormRef.value.validate()
    
    const token = route.query.token as string
    if (!token) {
      ElMessage.error('重置链接无效或已过期')
      router.push('/forgot-password')
      return
    }
    
    await userStore.resetPassword({
      token,
      password: resetForm.password,
      confirmPassword: resetForm.confirmPassword
    })
    
    // 成功后跳转到登录页
    setTimeout(() => {
      router.push('/login')
    }, 2000)
    
  } catch (error) {
    console.error('Reset password error:', error)
  }
}

onMounted(() => {
  // 检查是否有重置令牌
  if (!route.query.token) {
    ElMessage.error('重置链接无效或已过期')
    router.push('/forgot-password')
  }
})
</script>
