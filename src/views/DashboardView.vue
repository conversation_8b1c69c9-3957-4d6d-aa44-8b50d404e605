<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <img class="h-8 w-auto" src="@/assets/logo.svg" alt="Wise Video" />
            <h1 class="ml-3 text-xl font-semibold text-gray-900">Wise Video</h1>
          </div>
          
          <div class="flex items-center space-x-4">
            <el-dropdown>
              <span class="flex items-center space-x-2 cursor-pointer">
                <el-avatar :size="32" :src="userStore.user?.avatar">
                  {{ userStore.user?.username?.charAt(0).toUpperCase() }}
                </el-avatar>
                <span class="text-sm font-medium text-gray-700">{{ userStore.user?.username }}</span>
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="goToProfile">
                    <el-icon><User /></el-icon>
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item @click="goToSettings">
                    <el-icon><Setting /></el-icon>
                    设置
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="handleLogout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <el-icon class="h-6 w-6 text-blue-600"><VideoPlay /></el-icon>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">总视频数</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats.totalVideos }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <el-icon class="h-6 w-6 text-green-600"><View /></el-icon>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">总播放量</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats.totalViews }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <el-icon class="h-6 w-6 text-yellow-600"><Star /></el-icon>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">总点赞数</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats.totalLikes }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <el-icon class="h-6 w-6 text-purple-600"><User /></el-icon>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">粉丝数</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ stats.followers }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">快速操作</h3>
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <el-button type="primary" size="large" @click="uploadVideo">
              <el-icon><Upload /></el-icon>
              上传视频
            </el-button>
            <el-button size="large" @click="createPlaylist">
              <el-icon><FolderAdd /></el-icon>
              创建播放列表
            </el-button>
            <el-button size="large" @click="goToAnalytics">
              <el-icon><DataAnalysis /></el-icon>
              数据分析
            </el-button>
            <el-button size="large" @click="goToSettings">
              <el-icon><Setting /></el-icon>
              设置
            </el-button>
          </div>
        </div>
      </div>

      <!-- 最近视频 -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900">最近视频</h3>
            <el-button type="text" @click="viewAllVideos">查看全部</el-button>
          </div>
          
          <div v-if="recentVideos.length === 0" class="text-center py-12">
            <el-icon class="mx-auto h-12 w-12 text-gray-400"><VideoPlay /></el-icon>
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无视频</h3>
            <p class="mt-1 text-sm text-gray-500">开始上传您的第一个视频吧！</p>
            <div class="mt-6">
              <el-button type="primary" @click="uploadVideo">
                <el-icon><Upload /></el-icon>
                上传视频
              </el-button>
            </div>
          </div>
          
          <div v-else class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <div
              v-for="video in recentVideos"
              :key="video.id"
              class="bg-gray-50 rounded-lg overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
              @click="viewVideo(video.id)"
            >
              <div class="aspect-w-16 aspect-h-9 bg-gray-200">
                <img
                  :src="video.thumbnail"
                  :alt="video.title"
                  class="w-full h-full object-cover"
                />
              </div>
              <div class="p-4">
                <h4 class="text-sm font-medium text-gray-900 truncate">{{ video.title }}</h4>
                <p class="mt-1 text-xs text-gray-500">
                  {{ video.views }} 次观看 • {{ formatDate(video.createdAt) }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowDown,
  User,
  Setting,
  SwitchButton,
  VideoPlay,
  View,
  Star,
  Upload,
  FolderAdd,
  DataAnalysis
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 统计数据
const stats = ref({
  totalVideos: 0,
  totalViews: 0,
  totalLikes: 0,
  followers: 0
})

// 最近视频
const recentVideos = ref([])

// 获取统计数据
const fetchStats = async () => {
  try {
    // 这里应该调用实际的 API
    stats.value = {
      totalVideos: 12,
      totalViews: 15420,
      totalLikes: 892,
      followers: 156
    }
  } catch (error) {
    console.error('Failed to fetch stats:', error)
  }
}

// 获取最近视频
const fetchRecentVideos = async () => {
  try {
    // 这里应该调用实际的 API
    recentVideos.value = [
      {
        id: 1,
        title: '我的第一个视频',
        thumbnail: 'https://via.placeholder.com/300x200',
        views: 1200,
        createdAt: new Date('2024-01-15')
      },
      {
        id: 2,
        title: '教程视频',
        thumbnail: 'https://via.placeholder.com/300x200',
        views: 890,
        createdAt: new Date('2024-01-10')
      }
    ]
  } catch (error) {
    console.error('Failed to fetch recent videos:', error)
  }
}

// 格式化日期
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date)
}

// 导航方法
const uploadVideo = () => {
  router.push('/upload')
}

const createPlaylist = () => {
  router.push('/playlists/create')
}

const goToAnalytics = () => {
  router.push('/analytics')
}

const goToSettings = () => {
  router.push('/settings')
}

const goToProfile = () => {
  router.push('/profile')
}

const viewAllVideos = () => {
  router.push('/videos')
}

const viewVideo = (id: number) => {
  router.push(`/videos/${id}`)
}

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    userStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch {
    // 用户取消
  }
}

onMounted(() => {
  fetchStats()
  fetchRecentVideos()
})
</script> 