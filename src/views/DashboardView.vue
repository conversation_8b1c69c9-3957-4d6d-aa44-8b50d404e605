<template>
  <AppLayout>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">{{ t('dashboard.title') }}</h1>
        <p class="mt-2 text-gray-600">{{ t('dashboard.subtitle') }}</p>
      </div>

      <!-- 欢迎卡片 -->
      <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 mb-8 text-white">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-2xl font-bold mb-2">
              {{ t('dashboard.welcome', { name: userStore.user ? `${userStore.user.firstName} ${userStore.user.lastName}` : t('dashboard.user') }) }}
            </h2>
            <p class="text-blue-100">{{ t('dashboard.welcomeMessage') }}</p>
          </div>
          <div class="hidden md:block">
            <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center">
              <el-icon size="40" class="text-white">
                <User />
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div
          v-for="stat in stats"
          :key="stat.title"
          class="bg-white rounded-lg shadow p-6"
        >
          <div class="flex items-center">
            <div :class="[stat.iconBg, 'p-3 rounded-lg']">
              <el-icon :size="24" :class="stat.iconColor">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">{{ stat.title }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ stat.value }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- 快速创建 -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ t('dashboard.quickActions') }}</h3>
          <div class="grid grid-cols-2 gap-4">
            <button
              v-for="action in quickActions"
              :key="action.title"
              class="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors duration-200"
              @click="handleQuickAction(action.type)"
            >
              <el-icon :size="32" :class="action.iconColor" class="mb-2">
                <component :is="action.icon" />
              </el-icon>
              <span class="text-sm font-medium text-gray-700">{{ action.title }}</span>
            </button>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ t('dashboard.recentActivity') }}</h3>
          <div class="space-y-4">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="flex items-center space-x-3"
            >
              <div :class="[activity.iconBg, 'p-2 rounded-full']">
                <el-icon :size="16" :class="activity.iconColor">
                  <component :is="activity.icon" />
                </el-icon>
              </div>
              <div class="flex-1">
                <p class="text-sm text-gray-900">{{ activity.title }}</p>
                <p class="text-xs text-gray-500">{{ activity.time }}</p>
              </div>
            </div>
          </div>
          <div class="mt-4">
            <el-button type="primary" link>{{ t('dashboard.viewAll') }}</el-button>
          </div>
        </div>
      </div>

      <!-- 工具推荐 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ t('dashboard.recommendedTools') }}</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="tool in recommendedTools"
            :key="tool.name"
            class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
          >
            <div class="flex items-center mb-3">
              <div :class="[tool.iconBg, 'p-2 rounded-lg']">
                <el-icon :size="20" :class="tool.iconColor">
                  <component :is="tool.icon" />
                </el-icon>
              </div>
              <h4 class="ml-3 font-medium text-gray-900">{{ tool.name }}</h4>
            </div>
            <p class="text-sm text-gray-600 mb-3">{{ tool.description }}</p>
            <el-button type="primary" size="small" class="w-full">
              {{ t('dashboard.tryNow') }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import {
  User,
  PictureRounded,
  VideoPlay,
  Headphone,
  Box,
  TrendCharts,
  Clock,
  Star
} from '@element-plus/icons-vue'
import AppLayout from '@/components/AppLayout.vue'
import { useUserStore } from '@/stores/user'

const { t } = useI18n()
const userStore = useUserStore()

// 统计数据
const stats = computed(() => [
  {
    title: t('dashboard.stats.projects'),
    value: '12',
    icon: PictureRounded,
    iconColor: 'text-blue-600',
    iconBg: 'bg-blue-100'
  },
  {
    title: t('dashboard.stats.generated'),
    value: '48',
    icon: TrendCharts,
    iconColor: 'text-green-600',
    iconBg: 'bg-green-100'
  },
  {
    title: t('dashboard.stats.timeUsed'),
    value: '2.5h',
    icon: Clock,
    iconColor: 'text-purple-600',
    iconBg: 'bg-purple-100'
  },
  {
    title: t('dashboard.stats.credits'),
    value: '150',
    icon: Star,
    iconColor: 'text-yellow-600',
    iconBg: 'bg-yellow-100'
  }
])

// 快速操作
const quickActions = computed(() => [
  {
    title: t('dashboard.actions.generateImage'),
    type: 'image',
    icon: PictureRounded,
    iconColor: 'text-pink-600'
  },
  {
    title: t('dashboard.actions.createVideo'),
    type: 'video',
    icon: VideoPlay,
    iconColor: 'text-blue-600'
  },
  {
    title: t('dashboard.actions.generateAudio'),
    type: 'audio',
    icon: Headphone,
    iconColor: 'text-green-600'
  },
  {
    title: t('dashboard.actions.create3D'),
    type: '3d',
    icon: Box,
    iconColor: 'text-purple-600'
  }
])

// 最近活动
const recentActivities = computed(() => [
  {
    id: 1,
    title: t('dashboard.activity.imageGenerated'),
    time: t('dashboard.activity.timeAgo', { time: '2' }),
    icon: PictureRounded,
    iconColor: 'text-pink-600',
    iconBg: 'bg-pink-100'
  },
  {
    id: 2,
    title: t('dashboard.activity.videoCreated'),
    time: t('dashboard.activity.timeAgo', { time: '5' }),
    icon: VideoPlay,
    iconColor: 'text-blue-600',
    iconBg: 'bg-blue-100'
  },
  {
    id: 3,
    title: t('dashboard.activity.audioGenerated'),
    time: t('dashboard.activity.timeAgo', { time: '1' }),
    icon: Headphone,
    iconColor: 'text-green-600',
    iconBg: 'bg-green-100'
  }
])

// 推荐工具
const recommendedTools = computed(() => [
  {
    name: 'Flux-Dev',
    description: t('dashboard.tools.fluxDescription'),
    icon: PictureRounded,
    iconColor: 'text-pink-600',
    iconBg: 'bg-pink-100'
  },
  {
    name: 'Veo2-T2V',
    description: t('dashboard.tools.veoDescription'),
    icon: VideoPlay,
    iconColor: 'text-blue-600',
    iconBg: 'bg-blue-100'
  },
  {
    name: 'DIA-TTS',
    description: t('dashboard.tools.diaDescription'),
    icon: Headphone,
    iconColor: 'text-green-600',
    iconBg: 'bg-green-100'
  }
])

// 处理快速操作
const handleQuickAction = (type: string) => {
  ElMessage.info(t('dashboard.comingSoon'))
  // TODO: 实现具体的快速操作逻辑
}
</script>
