<template>
  <AppLayout>
    <div class="space-y-6">
      <!-- 欢迎信息 -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">
              欢迎回来，{{ userStore.user?.username }}！
            </h1>
            <p class="text-gray-600 mt-1">
              这是您的视频管理仪表板
            </p>
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" :icon="Upload" @click="$router.push('/upload')">
              上传视频
            </el-button>
            <el-button :icon="List" @click="$router.push('/playlists/create')">
              创建播放列表
            </el-button>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100">
              <el-icon class="text-blue-600 text-xl"><VideoPlay /></el-icon>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">总视频数</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.totalVideos }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100">
              <el-icon class="text-green-600 text-xl"><View /></el-icon>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">总观看次数</p>
              <p class="text-2xl font-semibold text-gray-900">{{ formatNumber(stats.totalViews) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100">
              <el-icon class="text-yellow-600 text-xl"><Star /></el-icon>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">总点赞数</p>
              <p class="text-2xl font-semibold text-gray-900">{{ formatNumber(stats.totalLikes) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100">
              <el-icon class="text-purple-600 text-xl"><User /></el-icon>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">关注者</p>
              <p class="text-2xl font-semibold text-gray-900">{{ formatNumber(stats.followers) }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近视频 -->
      <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-medium text-gray-900">最近上传的视频</h2>
            <router-link to="/videos" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
              查看全部
            </router-link>
          </div>
        </div>
        
        <div v-if="loading" class="p-6">
          <LoadingSpinner />
        </div>
        
        <div v-else-if="recentVideos.length === 0" class="p-6 text-center text-gray-500">
          <el-icon class="text-4xl mb-2"><VideoPlay /></el-icon>
          <p>还没有上传任何视频</p>
          <el-button type="primary" class="mt-4" @click="$router.push('/upload')">
            立即上传
          </el-button>
        </div>
        
        <div v-else class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <VideoCard
              v-for="video in recentVideos"
              :key="video.id"
              :video="video"
              :show-actions="true"
              @click="$router.push(`/videos/${video.id}`)"
              @edit="editVideo"
              @delete="deleteVideo"
            />
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, List, VideoPlay, View, Star, User } from '@element-plus/icons-vue'
import AppLayout from '@/components/AppLayout.vue'
import VideoCard from '@/components/VideoCard.vue'
import LoadingSpinner from '@/components/LoadingSpinner.vue'
import { useUserStore } from '@/stores/user'
import { videoService, analyticsService } from '@/services'
import type { Video, AnalyticsStats } from '@/types'

const userStore = useUserStore()

const loading = ref(false)
const recentVideos = ref<Video[]>([])
const stats = ref<AnalyticsStats>({
  totalVideos: 0,
  totalViews: 0,
  totalLikes: 0,
  followers: 0,
  monthlyViews: []
})

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const fetchDashboardData = async () => {
  loading.value = true
  try {
    // 获取统计数据
    const statsData = await analyticsService.getStats()
    stats.value = statsData

    // 获取最近的视频
    const videosData = await videoService.getVideos({ limit: 6 })
    recentVideos.value = videosData.videos
  } catch (error) {
    console.error('Failed to fetch dashboard data:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const editVideo = (video: Video) => {
  // 跳转到编辑页面
  // router.push(`/videos/${video.id}/edit`)
  ElMessage.info('编辑功能开发中')
}

const deleteVideo = async (video: Video) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除视频 "${video.title}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await videoService.deleteVideo(video.id)
    ElMessage.success('视频删除成功')
    
    // 重新获取数据
    fetchDashboardData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete video:', error)
      ElMessage.error('删除失败')
    }
  }
}

onMounted(() => {
  fetchDashboardData()
})
</script>
