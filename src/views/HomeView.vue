<template>
  <AppLayout>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-blue-600 to-purple-700 text-white">
      <div class="absolute inset-0 bg-black opacity-10"></div>
      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-6">
            {{ t('home.hero.title') }}
            <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-pink-400">
              {{ t('home.hero.titleHighlight') }}
            </span>
          </h1>
          <p class="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
            {{ t('home.hero.subtitle') }}
          </p>

          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <router-link
              to="/register"
              class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors duration-200 shadow-lg"
            >
              {{ t('home.hero.startCreating') }}
            </router-link>
            <router-link
              to="/about"
              class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-blue-600 transition-colors duration-200"
            >
              {{ t('home.hero.learnMore') }}
            </router-link>
          </div>

          <div class="mt-12 text-sm text-blue-200">
            <p>{{ t('home.hero.features') }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {{ t('home.features.title') }}
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            {{ t('home.features.subtitle') }}
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div
            v-for="feature in features"
            :key="feature.title"
            class="text-center p-6 rounded-xl hover:shadow-lg transition-shadow duration-300 border border-gray-100"
          >
            <div :class="['w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center', feature.bgColor]">
              <i :class="[feature.icon, 'text-2xl', feature.iconColor]"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ feature.title }}</h3>
            <p class="text-gray-600 mb-4">{{ feature.description }}</p>
            <div class="flex flex-wrap gap-2 justify-center">
              <span
                v-for="tool in feature.tools"
                :key="tool"
                class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
              >
                {{ tool }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {{ t('home.stats.title') }}
          </h2>
          <p class="text-xl text-gray-600">
            {{ t('home.stats.subtitle') }}
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div
            v-for="stat in stats"
            :key="stat.label"
            class="text-center"
          >
            <div class="text-4xl md:text-5xl font-bold text-blue-600 mb-2">
              {{ stat.value }}
            </div>
            <div class="text-gray-600 text-lg">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-purple-600 to-blue-600 text-white">
      <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold mb-6">
          {{ t('home.cta.title') }}
        </h2>
        <p class="text-xl mb-8 text-purple-100">
          {{ t('home.cta.subtitle') }}
        </p>

        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <router-link
            to="/register"
            class="bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors duration-200 shadow-lg"
          >
            {{ t('home.cta.signUpNow') }}
          </router-link>
          <router-link
            to="/about"
            class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-purple-600 transition-colors duration-200"
          >
            {{ t('home.cta.viewPricing') }}
          </router-link>
        </div>
      </div>
    </section>
  </AppLayout>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import AppLayout from '@/components/AppLayout.vue'

const { t } = useI18n()

const features = computed(() => [
  {
    title: t('home.features.imageGeneration.title'),
    description: t('home.features.imageGeneration.description'),
    icon: 'fas fa-image',
    iconColor: 'text-pink-600',
    bgColor: 'bg-pink-100',
    tools: ['Flux-Dev', 'SDXL', 'Midjourney']
  },
  {
    title: t('home.features.videoCreation.title'),
    description: t('home.features.videoCreation.description'),
    icon: 'fas fa-video',
    iconColor: 'text-blue-600',
    bgColor: 'bg-blue-100',
    tools: ['Veo2-T2V', 'Runway', 'Pika']
  },
  {
    title: t('home.features.audioSynthesis.title'),
    description: t('home.features.audioSynthesis.description'),
    icon: 'fas fa-music',
    iconColor: 'text-green-600',
    bgColor: 'bg-green-100',
    tools: ['DIA-TTS', 'MusicGen', 'AudioCraft']
  },
  {
    title: t('home.features.modeling3d.title'),
    description: t('home.features.modeling3d.description'),
    icon: 'fas fa-cube',
    iconColor: 'text-purple-600',
    bgColor: 'bg-purple-100',
    tools: ['Hunyuan3D-v2', 'Point-E', 'Shap-E']
  }
])

const stats = computed(() => [
  { value: '50K+', label: t('home.stats.activeUsers') },
  { value: '1M+', label: t('home.stats.generatedWorks') },
  { value: '99.9%', label: t('home.stats.uptime') },
  { value: '24/7', label: t('home.stats.support') }
])
</script>
